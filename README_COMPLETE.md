# 🌟 TheGND Platform

A comprehensive, secure, and modern platform for escort services built with cutting-edge technologies.

## 🚀 **Project Status: COMPLETE & PRODUCTION-READY**

✅ **All core features implemented and tested**  
✅ **Multi-role authentication system**  
✅ **Role-specific dashboards**  
✅ **File upload & gallery system**  
✅ **Booking & review system**  
✅ **Admin panel with moderation**  
✅ **Security & performance optimizations**  
✅ **Testing framework**  
✅ **Deployment scripts**

---

## 🏗️ **Architecture Overview**

### **Tech Stack**
- **Frontend:** Next.js 15, React 18, TypeScript
- **Styling:** Tailwind CSS 4, Shadcn/ui Components
- **Backend:** Next.js API Routes, Prisma ORM
- **Database:** PostgreSQL (Neon)
- **Authentication:** JWT with bcrypt
- **File Storage:** Local with Sharp optimization
- **Testing:** Jest, React Testing Library
- **Deployment:** Custom scripts with health checks

### **Key Features**
- 🔐 **Multi-role authentication** (Member, Escort, Agency, Studio, Admin)
- 📱 **Responsive design** for all devices
- 🖼️ **Advanced file upload** with image optimization
- 📊 **Real-time analytics** and performance monitoring
- 🛡️ **Enterprise-grade security** with rate limiting
- 🔍 **Advanced search & filtering**
- 💬 **Review & rating system**
- 📅 **Booking management**
- 👨‍💼 **Comprehensive admin panel**

---

## 🎯 **User Roles & Capabilities**

### **👤 Member (Client)**
- Browse and search profiles
- View detailed profile pages
- Book services
- Leave reviews and ratings
- Manage favorites
- Personal dashboard

### **💃 Escort**
- Complete profile management
- Gallery with photo/video uploads
- Availability calendar
- Booking management
- Earnings tracking
- Performance analytics

### **🏢 Agency**
- Manage multiple escort profiles
- Consolidated booking overview
- Team performance analytics
- Commission tracking
- Multi-escort coordination

### **🏠 Studio**
- Room management system
- Escort coordination
- Location-based services
- Facility management
- Occupancy tracking

### **👨‍💼 Admin**
- User management
- Content moderation
- System analytics
- Security monitoring
- Platform configuration

---

## 🚀 **Quick Start**

### **Prerequisites**
- Node.js 18+ 
- PostgreSQL database (Neon recommended)
- npm or yarn

### **Installation**

1. **Clone the repository**
```bash
git clone <repository-url>
cd thegnd
```

2. **Install dependencies**
```bash
npm install
```

3. **Environment setup**
```bash
cp .env.example .env
# Edit .env with your configuration
```

4. **Database setup**
```bash
npx prisma migrate dev
npx prisma db seed
```

5. **Start development server**
```bash
npm run dev
```

6. **Access the application**
- Frontend: http://localhost:3000
- Admin Panel: http://localhost:3000/admin

---

## 🔑 **Demo Accounts**

### **Test Credentials**
- **Admin:** <EMAIL> / Admin123!
- **Member:** <EMAIL> / Member123!
- **Escort:** <EMAIL> / Escort123!
- **Agency:** <EMAIL> / Agency123!

---

## 📁 **Project Structure**

```
src/
├── app/                    # Next.js 15 App Router
│   ├── api/               # API routes
│   ├── auth/              # Authentication pages
│   ├── dashboard/         # Role-specific dashboards
│   ├── admin/             # Admin panel
│   ├── browse/            # Profile browsing
│   └── profile/           # Profile pages
├── components/            # Reusable components
│   ├── ui/               # Shadcn/ui components
│   ├── dashboard/        # Dashboard widgets
│   ├── admin/            # Admin components
│   └── upload/           # File upload components
├── contexts/             # React contexts
├── lib/                  # Utilities and configurations
├── middleware/           # Custom middleware
├── types/                # TypeScript definitions
└── __tests__/            # Test files
```

---

## 🔐 **Security Features**

### **Authentication & Authorization**
- JWT-based authentication
- Role-based access control (RBAC)
- Password strength validation
- Email verification
- Session management

### **Security Measures**
- Rate limiting on all endpoints
- Input sanitization and validation
- SQL injection prevention
- XSS protection
- CSRF protection
- Security headers (CSP, HSTS, etc.)

### **Data Protection**
- Password hashing with bcrypt
- Sensitive data encryption
- Audit logging
- GDPR compliance ready

---

## 📊 **Performance Optimizations**

### **Frontend**
- Next.js 15 with App Router
- Image optimization with Sharp
- Lazy loading and code splitting
- Responsive images
- Bundle optimization

### **Backend**
- Database query optimization
- Caching strategies
- Connection pooling
- Performance monitoring
- Health checks

### **Monitoring**
- Real-time performance metrics
- Error tracking
- Memory usage monitoring
- Database performance
- API response times

---

## 🧪 **Testing**

### **Test Coverage**
- Unit tests for API routes
- Integration tests for authentication
- Component testing with React Testing Library
- Security testing
- Performance testing

### **Running Tests**
```bash
# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run specific test suite
npm test auth.test.ts
```

---

## 🚀 **Deployment**

### **Production Deployment**
```bash
# Full deployment
./scripts/deploy.sh

# Individual steps
./scripts/deploy.sh backup    # Database backup
./scripts/deploy.sh test      # Run tests
./scripts/deploy.sh build     # Build application
./scripts/deploy.sh migrate   # Database migration
./scripts/deploy.sh health    # Health check
```

### **Environment Variables**
```env
# Database
DATABASE_URL="postgresql://..."

# Authentication
JWT_SECRET="your-secret-key"
JWT_EXPIRES_IN="24h"

# Email (optional)
SMTP_HOST="smtp.example.com"
SMTP_USER="<EMAIL>"
SMTP_PASS="password"

# File Upload
UPLOAD_MAX_SIZE="10485760"  # 10MB
UPLOAD_ALLOWED_TYPES="jpg,jpeg,png,webp,mp4,webm"

# Security
RATE_LIMIT_WINDOW="900000"  # 15 minutes
RATE_LIMIT_MAX="100"        # Max requests per window
```

---

## 📈 **API Documentation**

### **Authentication Endpoints**
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/logout` - User logout
- `GET /api/auth/me` - Get current user

### **Profile Endpoints**
- `GET /api/profiles` - List profiles
- `GET /api/profiles/[id]` - Get profile details
- `PUT /api/profiles/[id]` - Update profile
- `DELETE /api/profiles/[id]` - Delete profile

### **Booking Endpoints**
- `POST /api/bookings` - Create booking
- `GET /api/bookings` - List bookings
- `GET /api/bookings/[id]` - Get booking details
- `PATCH /api/bookings/[id]` - Update booking
- `DELETE /api/bookings/[id]` - Cancel booking

### **Upload Endpoints**
- `POST /api/upload` - Upload files
- `GET /api/media` - List media
- `DELETE /api/media` - Delete media

### **Review Endpoints**
- `POST /api/reviews` - Create review
- `GET /api/reviews` - List reviews

---

## 🛠️ **Development**

### **Available Scripts**
```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run start        # Start production server
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking
npm test             # Run tests
npm run db:migrate   # Run database migrations
npm run db:seed      # Seed database
npm run db:studio    # Open Prisma Studio
```

### **Code Quality**
- ESLint configuration
- Prettier formatting
- TypeScript strict mode
- Husky pre-commit hooks
- Conventional commits

---

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new features
5. Ensure all tests pass
6. Submit a pull request

---

## 📄 **License**

This project is licensed under the MIT License - see the LICENSE file for details.

---

## 🆘 **Support**

For support and questions:
- Create an issue on GitHub
- Check the documentation
- Review the test files for examples

---

## 🎉 **Acknowledgments**

- Next.js team for the amazing framework
- Prisma team for the excellent ORM
- Shadcn for the beautiful UI components
- Tailwind CSS for the utility-first CSS framework
- All contributors and testers

---

**🚀 The platform is now complete and ready for production deployment!**
