#!/bin/bash

# TheGND Platform Deployment Script
# This script handles the deployment process for the platform

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
PROJECT_NAME="thegnd"
DOCKER_IMAGE="thegnd-platform"
DOCKER_TAG="latest"
BACKUP_DIR="./backups"
LOG_FILE="./deploy.log"

# Functions
log() {
    echo -e "${BLUE}[$(date +'%Y-%m-%d %H:%M:%S')]${NC} $1" | tee -a "$LOG_FILE"
}

error() {
    echo -e "${RED}[ERROR]${NC} $1" | tee -a "$LOG_FILE"
    exit 1
}

warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1" | tee -a "$LOG_FILE"
}

success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" | tee -a "$LOG_FILE"
}

# Check prerequisites
check_prerequisites() {
    log "Checking prerequisites..."
    
    # Check if Node.js is installed
    if ! command -v node &> /dev/null; then
        error "Node.js is not installed. Please install Node.js 18 or higher."
    fi
    
    # Check Node.js version
    NODE_VERSION=$(node -v | cut -d'v' -f2 | cut -d'.' -f1)
    if [ "$NODE_VERSION" -lt 18 ]; then
        error "Node.js version 18 or higher is required. Current version: $(node -v)"
    fi
    
    # Check if npm is installed
    if ! command -v npm &> /dev/null; then
        error "npm is not installed."
    fi
    
    # Check if git is installed
    if ! command -v git &> /dev/null; then
        error "git is not installed."
    fi
    
    # Check if .env file exists
    if [ ! -f ".env" ]; then
        error ".env file not found. Please create one based on .env.example"
    fi
    
    success "Prerequisites check passed"
}

# Backup database
backup_database() {
    log "Creating database backup..."
    
    mkdir -p "$BACKUP_DIR"
    
    # Create timestamp for backup
    TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
    BACKUP_FILE="$BACKUP_DIR/backup_$TIMESTAMP.sql"
    
    # Check if DATABASE_URL is set
    if [ -z "$DATABASE_URL" ]; then
        warning "DATABASE_URL not set, skipping database backup"
        return
    fi
    
    # Extract database info from URL
    DB_URL="$DATABASE_URL"
    
    # For Neon/PostgreSQL backup
    if [[ $DB_URL == postgres* ]]; then
        log "Backing up PostgreSQL database..."
        pg_dump "$DB_URL" > "$BACKUP_FILE" 2>/dev/null || {
            warning "Database backup failed, continuing deployment..."
            return
        }
        success "Database backup created: $BACKUP_FILE"
    else
        warning "Unsupported database type for backup"
    fi
}

# Install dependencies
install_dependencies() {
    log "Installing dependencies..."
    
    # Clean install
    rm -rf node_modules package-lock.json
    npm ci
    
    success "Dependencies installed"
}

# Run tests
run_tests() {
    log "Running tests..."
    
    # Type checking
    log "Running type check..."
    npm run type-check || error "Type check failed"
    
    # Linting
    log "Running linter..."
    npm run lint || error "Linting failed"
    
    # Unit tests
    if [ -f "jest.config.js" ] || [ -f "jest.config.ts" ]; then
        log "Running unit tests..."
        npm run test || error "Tests failed"
    else
        warning "No test configuration found, skipping tests"
    fi
    
    success "All tests passed"
}

# Build application
build_application() {
    log "Building application..."
    
    # Generate Prisma client
    log "Generating Prisma client..."
    npx prisma generate
    
    # Build Next.js application
    log "Building Next.js application..."
    npm run build || error "Build failed"
    
    success "Application built successfully"
}

# Database migration
migrate_database() {
    log "Running database migrations..."
    
    # Deploy migrations
    npx prisma migrate deploy || error "Database migration failed"
    
    success "Database migrations completed"
}

# Start application
start_application() {
    log "Starting application..."
    
    # Kill existing process if running
    if pgrep -f "next start" > /dev/null; then
        log "Stopping existing application..."
        pkill -f "next start"
        sleep 5
    fi
    
    # Start application in background
    nohup npm start > app.log 2>&1 &
    APP_PID=$!
    
    # Wait a moment and check if process is still running
    sleep 5
    if kill -0 $APP_PID 2>/dev/null; then
        success "Application started successfully (PID: $APP_PID)"
    else
        error "Application failed to start"
    fi
}

# Health check
health_check() {
    log "Performing health check..."
    
    local max_attempts=30
    local attempt=1
    local health_url="http://localhost:3000/api/health"
    
    while [ $attempt -le $max_attempts ]; do
        log "Health check attempt $attempt/$max_attempts..."
        
        if curl -f -s "$health_url" > /dev/null 2>&1; then
            success "Health check passed"
            return 0
        fi
        
        sleep 10
        ((attempt++))
    done
    
    error "Health check failed after $max_attempts attempts"
}

# Cleanup old backups
cleanup_backups() {
    log "Cleaning up old backups..."
    
    if [ -d "$BACKUP_DIR" ]; then
        # Keep only last 10 backups
        ls -t "$BACKUP_DIR"/backup_*.sql 2>/dev/null | tail -n +11 | xargs -r rm
        success "Old backups cleaned up"
    fi
}

# Send notification
send_notification() {
    local status=$1
    local message=$2
    
    log "Sending deployment notification..."
    
    # You can integrate with Slack, Discord, email, etc.
    # Example webhook notification:
    if [ -n "$WEBHOOK_URL" ]; then
        curl -X POST "$WEBHOOK_URL" \
            -H "Content-Type: application/json" \
            -d "{\"text\":\"🚀 Deployment $status: $message\"}" \
            > /dev/null 2>&1 || warning "Failed to send notification"
    fi
}

# Rollback function
rollback() {
    log "Rolling back deployment..."
    
    # Stop current application
    if pgrep -f "next start" > /dev/null; then
        pkill -f "next start"
    fi
    
    # Restore from latest backup if available
    LATEST_BACKUP=$(ls -t "$BACKUP_DIR"/backup_*.sql 2>/dev/null | head -n1)
    if [ -n "$LATEST_BACKUP" ] && [ -n "$DATABASE_URL" ]; then
        log "Restoring database from backup: $LATEST_BACKUP"
        psql "$DATABASE_URL" < "$LATEST_BACKUP" || warning "Database restore failed"
    fi
    
    error "Deployment rolled back"
}

# Main deployment function
deploy() {
    log "Starting deployment of $PROJECT_NAME..."
    
    # Trap errors for rollback
    trap rollback ERR
    
    # Load environment variables
    if [ -f ".env" ]; then
        export $(cat .env | grep -v '^#' | xargs)
    fi
    
    # Deployment steps
    check_prerequisites
    backup_database
    install_dependencies
    run_tests
    build_application
    migrate_database
    start_application
    health_check
    cleanup_backups
    
    # Remove error trap
    trap - ERR
    
    success "Deployment completed successfully!"
    send_notification "SUCCESS" "Platform deployed successfully"
}

# Parse command line arguments
case "${1:-deploy}" in
    "deploy")
        deploy
        ;;
    "backup")
        backup_database
        ;;
    "test")
        run_tests
        ;;
    "build")
        build_application
        ;;
    "migrate")
        migrate_database
        ;;
    "health")
        health_check
        ;;
    "rollback")
        rollback
        ;;
    *)
        echo "Usage: $0 {deploy|backup|test|build|migrate|health|rollback}"
        echo ""
        echo "Commands:"
        echo "  deploy   - Full deployment process (default)"
        echo "  backup   - Create database backup only"
        echo "  test     - Run tests only"
        echo "  build    - Build application only"
        echo "  migrate  - Run database migrations only"
        echo "  health   - Perform health check only"
        echo "  rollback - Rollback deployment"
        exit 1
        ;;
esac
