import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import sharp from 'sharp';
import { MediaType } from '@/types';

const UPLOAD_DIR = process.env.UPLOAD_DIR || 'public/uploads';
const MAX_FILE_SIZE = parseInt(process.env.MAX_FILE_SIZE || '10485760'); // 10MB

export interface ProcessedFile {
  filename: string;
  originalName: string;
  fileUrl: string;
  thumbnailUrl?: string;
  fileSize: number;
  mimeType: string;
  type: MediaType;
  width?: number;
  height?: number;
  duration?: number;
}

export async function processUpload(file: File, subfolder: string = ''): Promise<ProcessedFile> {
  // Validate file size
  if (file.size > MAX_FILE_SIZE) {
    throw new Error(`File size exceeds maximum limit of ${MAX_FILE_SIZE / 1024 / 1024}MB`);
  }

  // Validate file type
  const allowedTypes = [
    'image/jpeg',
    'image/png', 
    'image/webp',
    'video/mp4',
    'video/webm'
  ];

  if (!allowedTypes.includes(file.type)) {
    throw new Error('File type not allowed');
  }

  // Generate unique filename
  const timestamp = Date.now();
  const randomString = Math.random().toString(36).substring(2, 15);
  const extension = path.extname(file.name);
  const filename = `${timestamp}-${randomString}${extension}`;

  // Create upload directory if it doesn't exist
  const uploadPath = path.join(process.cwd(), UPLOAD_DIR, subfolder);
  if (!existsSync(uploadPath)) {
    await mkdir(uploadPath, { recursive: true });
  }

  // Convert file to buffer
  const bytes = await file.arrayBuffer();
  const buffer = Buffer.from(bytes);

  // Determine media type
  const mediaType: MediaType = file.type.startsWith('image/') ? MediaType.IMAGE : MediaType.VIDEO;

  let processedFile: ProcessedFile = {
    filename,
    originalName: file.name,
    fileUrl: `/${UPLOAD_DIR}/${subfolder}/${filename}`.replace('//', '/'),
    fileSize: file.size,
    mimeType: file.type,
    type: mediaType,
  };

  if (mediaType === MediaType.IMAGE) {
    // Process image with Sharp
    const image = sharp(buffer);
    const metadata = await image.metadata();

    processedFile.width = metadata.width;
    processedFile.height = metadata.height;

    // Save original image
    const filePath = path.join(uploadPath, filename);
    await writeFile(filePath, buffer);

    // Create thumbnail
    const thumbnailFilename = `thumb-${filename}`;
    const thumbnailPath = path.join(uploadPath, thumbnailFilename);
    
    await image
      .resize(300, 300, { 
        fit: 'cover',
        position: 'center'
      })
      .jpeg({ quality: 80 })
      .toFile(thumbnailPath);

    processedFile.thumbnailUrl = `/${UPLOAD_DIR}/${subfolder}/${thumbnailFilename}`.replace('//', '/');
  } else {
    // For videos, just save the file
    // In a production environment, you might want to use FFmpeg for video processing
    const filePath = path.join(uploadPath, filename);
    await writeFile(filePath, buffer);

    // Create a simple thumbnail for videos (you'd typically use FFmpeg for this)
    // For now, we'll just set a placeholder
    processedFile.thumbnailUrl = '/placeholder-video-thumb.jpg';
  }

  return processedFile;
}

export async function deleteFile(filePath: string): Promise<void> {
  try {
    const fullPath = path.join(process.cwd(), filePath);
    if (existsSync(fullPath)) {
      await import('fs/promises').then(fs => fs.unlink(fullPath));
    }
  } catch (error) {
    console.error('Error deleting file:', error);
  }
}

export function getFileUrl(filename: string, subfolder: string = ''): string {
  return `/${UPLOAD_DIR}/${subfolder}/${filename}`.replace('//', '/');
}

export function validateFileType(file: File): boolean {
  const allowedTypes = [
    'image/jpeg',
    'image/png',
    'image/webp', 
    'video/mp4',
    'video/webm'
  ];
  return allowedTypes.includes(file.type);
}

export function validateFileSize(file: File): boolean {
  return file.size <= MAX_FILE_SIZE;
}

export function getMediaType(mimeType: string): MediaType {
  return mimeType.startsWith('image/') ? MediaType.IMAGE : MediaType.VIDEO;
}
