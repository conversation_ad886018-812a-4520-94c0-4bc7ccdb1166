import { z } from 'zod';

// User validation schemas
export const registerSchema = z.object({
  username: z
    .string()
    .min(3, 'Username must be at least 3 characters')
    .max(30, 'Username must be less than 30 characters')
    .regex(/^[a-zA-Z0-9_]+$/, 'Username can only contain letters, numbers, and underscores'),
  email: z.string().email('Invalid email address'),
  password: z
    .string()
    .min(8, 'Password must be at least 8 characters')
    .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
    .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
    .regex(/\d/, 'Password must contain at least one number')
    .regex(/[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]/, 'Password must contain at least one special character'),
  confirmPassword: z.string(),
  role: z.enum(['MEMBER', 'ESCORT', 'AGENCY', 'STUDIO', 'ADMIN']),
  agreeToTerms: z.boolean().refine(val => val === true, 'You must agree to the terms and conditions'),
}).refine(data => data.password === data.confirmPassword, {
  message: "Passwords don't match",
  path: ["confirmPassword"],
});

export const loginSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(1, 'Password is required'),
});

// Profile validation schemas
export const profileSchema = z.object({
  name: z.string().min(1, 'Name is required').max(100, 'Name must be less than 100 characters'),
  alias: z.string().max(50, 'Alias must be less than 50 characters').optional(),
  location: z.string().min(1, 'Location is required').max(100, 'Location must be less than 100 characters'),
  description: z.string().max(2000, 'Description must be less than 2000 characters').optional(),
  phone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format').optional(),
  website: z.string().url('Invalid website URL').optional(),
  age: z.number().min(18, 'Must be at least 18 years old').max(100, 'Invalid age').optional(),
  gender: z.enum(['MALE', 'FEMALE', 'TRANS', 'NON_BINARY', 'OTHER']).optional(),
  height: z.number().min(100, 'Height must be at least 100cm').max(250, 'Height must be less than 250cm').optional(),
  weight: z.number().min(30, 'Weight must be at least 30kg').max(300, 'Weight must be less than 300kg').optional(),
  languages: z.array(z.string()).default([]),
  hourlyRate: z.number().min(0, 'Rate must be positive').optional(),
});

// Service validation schema
export const serviceSchema = z.object({
  name: z.string().min(1, 'Service name is required').max(100, 'Name must be less than 100 characters'),
  description: z.string().max(500, 'Description must be less than 500 characters').optional(),
  price: z.number().min(0, 'Price must be positive'),
  duration: z.number().min(15, 'Duration must be at least 15 minutes').max(1440, 'Duration must be less than 24 hours'),
});

// Booking validation schema
export const bookingSchema = z.object({
  profileId: z.string().cuid('Invalid profile ID'),
  serviceId: z.string().cuid('Invalid service ID').optional(),
  startTime: z.string().datetime('Invalid start time'),
  endTime: z.string().datetime('Invalid end time'),
  clientName: z.string().min(1, 'Client name is required').max(100, 'Name must be less than 100 characters'),
  clientEmail: z.string().email('Invalid email address'),
  clientPhone: z.string().regex(/^\+?[\d\s\-\(\)]+$/, 'Invalid phone number format').optional(),
  notes: z.string().max(500, 'Notes must be less than 500 characters').optional(),
}).refine(data => new Date(data.endTime) > new Date(data.startTime), {
  message: "End time must be after start time",
  path: ["endTime"],
});

// Review validation schema
export const reviewSchema = z.object({
  bookingId: z.string().cuid('Invalid booking ID'),
  rating: z.number().min(1, 'Rating must be at least 1').max(5, 'Rating must be at most 5'),
  comment: z.string().max(1000, 'Comment must be less than 1000 characters').optional(),
});

// Search validation schema
export const searchSchema = z.object({
  query: z.string().optional(),
  location: z.string().optional(),
  gender: z.enum(['MALE', 'FEMALE', 'TRANS', 'NON_BINARY', 'OTHER']).optional(),
  ageMin: z.number().min(18).max(100).optional(),
  ageMax: z.number().min(18).max(100).optional(),
  priceMin: z.number().min(0).optional(),
  priceMax: z.number().min(0).optional(),
  languages: z.array(z.string()).optional(),
  services: z.array(z.string()).optional(),
  isAvailable: z.boolean().optional(),
  page: z.number().min(1).default(1),
  limit: z.number().min(1).max(100).default(20),
  sortBy: z.enum(['name', 'price', 'rating', 'createdAt']).default('createdAt'),
  sortOrder: z.enum(['asc', 'desc']).default('desc'),
});

// File upload validation
export const fileUploadSchema = z.object({
  file: z.any().refine(
    (file) => file instanceof File,
    'File is required'
  ).refine(
    (file) => file.size <= 10 * 1024 * 1024, // 10MB
    'File size must be less than 10MB'
  ).refine(
    (file) => ['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/webm'].includes(file.type),
    'File must be an image (JPEG, PNG, WebP) or video (MP4, WebM)'
  ),
});

// CMS Page validation
export const pageSchema = z.object({
  slug: z.string().min(1, 'Slug is required').regex(/^[a-z0-9-]+$/, 'Slug can only contain lowercase letters, numbers, and hyphens'),
  title: z.string().min(1, 'Title is required').max(200, 'Title must be less than 200 characters'),
  content: z.string().min(1, 'Content is required'),
  isPublished: z.boolean().default(false),
});

// Settings validation
export const settingSchema = z.object({
  key: z.string().min(1, 'Key is required'),
  value: z.string().min(1, 'Value is required'),
});

export type RegisterInput = z.infer<typeof registerSchema>;
export type LoginInput = z.infer<typeof loginSchema>;
export type ProfileInput = z.infer<typeof profileSchema>;
export type ServiceInput = z.infer<typeof serviceSchema>;
export type BookingInput = z.infer<typeof bookingSchema>;
export type ReviewInput = z.infer<typeof reviewSchema>;
export type SearchInput = z.infer<typeof searchSchema>;
export type FileUploadInput = z.infer<typeof fileUploadSchema>;
export type PageInput = z.infer<typeof pageSchema>;
export type SettingInput = z.infer<typeof settingSchema>;
