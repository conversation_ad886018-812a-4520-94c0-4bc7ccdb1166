// Performance monitoring and optimization utilities

// Cache configuration
export const CACHE_CONFIG = {
  // Static content cache (1 year)
  STATIC: 365 * 24 * 60 * 60,
  
  // API responses cache (5 minutes)
  API: 5 * 60,
  
  // User data cache (1 hour)
  USER_DATA: 60 * 60,
  
  // Profile data cache (30 minutes)
  PROFILE_DATA: 30 * 60,
  
  // Search results cache (10 minutes)
  SEARCH_RESULTS: 10 * 60,
};

// In-memory cache (in production, use Redis)
const memoryCache = new Map<string, { data: any; expires: number }>();

export class CacheManager {
  static set(key: string, data: any, ttl: number): void {
    const expires = Date.now() + (ttl * 1000);
    memoryCache.set(key, { data, expires });
  }

  static get<T>(key: string): T | null {
    const cached = memoryCache.get(key);
    
    if (!cached) {
      return null;
    }
    
    if (Date.now() > cached.expires) {
      memoryCache.delete(key);
      return null;
    }
    
    return cached.data as T;
  }

  static delete(key: string): void {
    memoryCache.delete(key);
  }

  static clear(): void {
    memoryCache.clear();
  }

  static cleanup(): void {
    const now = Date.now();
    for (const [key, value] of memoryCache.entries()) {
      if (now > value.expires) {
        memoryCache.delete(key);
      }
    }
  }

  static generateKey(...parts: string[]): string {
    return parts.join(':');
  }
}

// Performance monitoring
export class PerformanceMonitor {
  private static metrics = new Map<string, number[]>();

  static startTimer(label: string): () => number {
    const start = performance.now();
    
    return () => {
      const duration = performance.now() - start;
      this.recordMetric(label, duration);
      return duration;
    };
  }

  static recordMetric(label: string, value: number): void {
    if (!this.metrics.has(label)) {
      this.metrics.set(label, []);
    }
    
    const values = this.metrics.get(label)!;
    values.push(value);
    
    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }

  static getMetrics(label: string): {
    count: number;
    average: number;
    min: number;
    max: number;
    p95: number;
  } | null {
    const values = this.metrics.get(label);
    
    if (!values || values.length === 0) {
      return null;
    }
    
    const sorted = [...values].sort((a, b) => a - b);
    const count = values.length;
    const sum = values.reduce((a, b) => a + b, 0);
    
    return {
      count,
      average: sum / count,
      min: sorted[0],
      max: sorted[sorted.length - 1],
      p95: sorted[Math.floor(count * 0.95)],
    };
  }

  static getAllMetrics(): Record<string, any> {
    const result: Record<string, any> = {};
    
    for (const label of this.metrics.keys()) {
      result[label] = this.getMetrics(label);
    }
    
    return result;
  }
}

// Database query optimization
export class QueryOptimizer {
  static async withCache<T>(
    key: string,
    queryFn: () => Promise<T>,
    ttl: number = CACHE_CONFIG.API
  ): Promise<T> {
    // Try cache first
    const cached = CacheManager.get<T>(key);
    if (cached !== null) {
      return cached;
    }

    // Execute query with performance monitoring
    const endTimer = PerformanceMonitor.startTimer(`query:${key}`);
    
    try {
      const result = await queryFn();
      
      // Cache the result
      CacheManager.set(key, result, ttl);
      
      return result;
    } finally {
      endTimer();
    }
  }

  static async batchQueries<T>(
    queries: Array<() => Promise<T>>
  ): Promise<T[]> {
    const endTimer = PerformanceMonitor.startTimer('batch_queries');
    
    try {
      return await Promise.all(queries.map(query => query()));
    } finally {
      endTimer();
    }
  }
}

// Image optimization
export interface ImageOptimizationOptions {
  width?: number;
  height?: number;
  quality?: number;
  format?: 'webp' | 'jpeg' | 'png';
  fit?: 'cover' | 'contain' | 'fill';
}

export class ImageOptimizer {
  static generateOptimizedUrl(
    originalUrl: string,
    options: ImageOptimizationOptions = {}
  ): string {
    const params = new URLSearchParams();
    
    if (options.width) params.set('w', options.width.toString());
    if (options.height) params.set('h', options.height.toString());
    if (options.quality) params.set('q', options.quality.toString());
    if (options.format) params.set('f', options.format);
    if (options.fit) params.set('fit', options.fit);
    
    const queryString = params.toString();
    return queryString ? `${originalUrl}?${queryString}` : originalUrl;
  }

  static getResponsiveSizes(baseWidth: number): string {
    const sizes = [320, 640, 768, 1024, 1280, 1536];
    return sizes
      .filter(size => size <= baseWidth * 2)
      .map(size => `${size}w`)
      .join(', ');
  }
}

// API response optimization
export class ResponseOptimizer {
  static compress(data: any): string {
    // In production, use proper compression library
    return JSON.stringify(data);
  }

  static paginate<T>(
    items: T[],
    page: number = 1,
    limit: number = 10
  ): {
    data: T[];
    pagination: {
      page: number;
      limit: number;
      total: number;
      pages: number;
      hasNext: boolean;
      hasPrev: boolean;
    };
  } {
    const total = items.length;
    const pages = Math.ceil(total / limit);
    const start = (page - 1) * limit;
    const end = start + limit;
    
    return {
      data: items.slice(start, end),
      pagination: {
        page,
        limit,
        total,
        pages,
        hasNext: page < pages,
        hasPrev: page > 1,
      },
    };
  }

  static filterSensitiveData(data: any, userRole: string): any {
    if (!data) return data;
    
    // Clone the data to avoid mutations
    const filtered = JSON.parse(JSON.stringify(data));
    
    // Remove sensitive fields based on user role
    if (userRole !== 'ADMIN') {
      delete filtered.email;
      delete filtered.phone;
      delete filtered.internalNotes;
    }
    
    if (userRole === 'GUEST') {
      delete filtered.lastLogin;
      delete filtered.createdAt;
    }
    
    return filtered;
  }
}

// Bundle optimization
export class BundleOptimizer {
  static async loadComponent<T>(
    importFn: () => Promise<{ default: T }>
  ): Promise<T> {
    const endTimer = PerformanceMonitor.startTimer('component_load');
    
    try {
      const module = await importFn();
      return module.default;
    } finally {
      endTimer();
    }
  }

  static preloadRoute(route: string): void {
    if (typeof window !== 'undefined') {
      const link = document.createElement('link');
      link.rel = 'prefetch';
      link.href = route;
      document.head.appendChild(link);
    }
  }
}

// Memory management
export class MemoryManager {
  private static intervals: NodeJS.Timeout[] = [];

  static startCleanupInterval(intervalMs: number = 5 * 60 * 1000): void {
    const interval = setInterval(() => {
      CacheManager.cleanup();
      this.garbageCollect();
    }, intervalMs);
    
    this.intervals.push(interval);
  }

  static stopCleanupIntervals(): void {
    this.intervals.forEach(interval => clearInterval(interval));
    this.intervals = [];
  }

  private static garbageCollect(): void {
    if (global.gc) {
      global.gc();
    }
  }

  static getMemoryUsage(): NodeJS.MemoryUsage {
    return process.memoryUsage();
  }
}

// Performance middleware
export function performanceMiddleware() {
  return (req: any, res: any, next: any) => {
    const endTimer = PerformanceMonitor.startTimer('request_duration');
    
    // Add performance headers
    res.setHeader('X-Response-Time-Start', Date.now().toString());
    
    // Override res.end to capture timing
    const originalEnd = res.end;
    res.end = function(...args: any[]) {
      const duration = endTimer();
      res.setHeader('X-Response-Time', `${duration.toFixed(2)}ms`);
      originalEnd.apply(this, args);
    };
    
    next();
  };
}

// Health check utilities
export class HealthChecker {
  static async checkDatabase(): Promise<boolean> {
    try {
      // Simple database ping
      const { prisma } = await import('./prisma');
      await prisma.$queryRaw`SELECT 1`;
      return true;
    } catch {
      return false;
    }
  }

  static checkMemory(): { healthy: boolean; usage: NodeJS.MemoryUsage } {
    const usage = MemoryManager.getMemoryUsage();
    const maxHeapUsed = usage.heapUsed / usage.heapTotal;
    
    return {
      healthy: maxHeapUsed < 0.9, // Alert if heap usage > 90%
      usage,
    };
  }

  static async getHealthStatus(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    checks: Record<string, boolean>;
    timestamp: string;
  }> {
    const checks = {
      database: await this.checkDatabase(),
      memory: this.checkMemory().healthy,
    };
    
    const allHealthy = Object.values(checks).every(Boolean);
    const someHealthy = Object.values(checks).some(Boolean);
    
    let status: 'healthy' | 'degraded' | 'unhealthy';
    if (allHealthy) {
      status = 'healthy';
    } else if (someHealthy) {
      status = 'degraded';
    } else {
      status = 'unhealthy';
    }
    
    return {
      status,
      checks,
      timestamp: new Date().toISOString(),
    };
  }
}

// Initialize performance monitoring
if (typeof window === 'undefined') {
  // Server-side initialization
  MemoryManager.startCleanupInterval();
}
