'use client';

import { useState, useEffect } from 'react';
import { useParams } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Heart, 
  Star, 
  MapPin, 
  Calendar, 
  Clock,
  Phone,
  Globe,
  User,
  Shield,
  CheckCircle,
  Image as ImageIcon,
  Video,
  ArrowLeft
} from 'lucide-react';
import Link from 'next/link';

interface ProfileData {
  id: string;
  name: string;
  alias?: string;
  description: string;
  location: string;
  profileType: string;
  age?: number;
  gender?: string;
  height?: number;
  weight?: number;
  languages: string[];
  hourlyRate?: number;
  profileImage?: string;
  coverImage?: string;
  isAvailable: boolean;
  phone?: string;
  website?: string;
  companyName?: string;
  averageRating: number;
  reviewCount: number;
  favoriteCount: number;
  media: MediaItem[];
  services: ServiceItem[];
  reviews: ReviewItem[];
  createdAt: string;
}

interface MediaItem {
  id: string;
  fileUrl: string;
  thumbnailUrl?: string;
  type: 'IMAGE' | 'VIDEO';
  status: string;
}

interface ServiceItem {
  id: string;
  name: string;
  description: string;
  price: number;
  duration: number;
}

interface ReviewItem {
  id: string;
  rating: number;
  comment: string;
  clientName: string;
  createdAt: string;
}

export default function ProfilePage() {
  const params = useParams();
  const { user } = useAuth();
  const [profile, setProfile] = useState<ProfileData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [isFavorite, setIsFavorite] = useState(false);
  const [selectedMediaIndex, setSelectedMediaIndex] = useState(0);

  useEffect(() => {
    const fetchProfile = async () => {
      try {
        // Mock data for now - in real app, fetch from API
        setTimeout(() => {
          setProfile({
            id: params.id as string,
            name: 'Sophia Elite',
            alias: 'Sophia',
            description: 'Professional companion offering premium services in Berlin. Elegant, sophisticated, and discreet. Available for dinner dates, social events, and private encounters.',
            location: 'Berlin, Germany',
            profileType: 'ESCORT',
            age: 26,
            gender: 'FEMALE',
            height: 170,
            weight: 55,
            languages: ['German', 'English', 'French'],
            hourlyRate: 300,
            profileImage: '/placeholder-avatar.jpg',
            coverImage: '/placeholder-cover.jpg',
            isAvailable: true,
            phone: '+49 ************',
            website: 'https://sophia-elite.com',
            averageRating: 4.8,
            reviewCount: 32,
            favoriteCount: 89,
            media: [
              {
                id: '1',
                fileUrl: '/placeholder-image.jpg',
                thumbnailUrl: '/placeholder-thumb.jpg',
                type: 'IMAGE',
                status: 'APPROVED',
              },
              {
                id: '2',
                fileUrl: '/placeholder-image-2.jpg',
                thumbnailUrl: '/placeholder-thumb-2.jpg',
                type: 'IMAGE',
                status: 'APPROVED',
              },
              {
                id: '3',
                fileUrl: '/placeholder-video.mp4',
                thumbnailUrl: '/placeholder-video-thumb.jpg',
                type: 'VIDEO',
                status: 'APPROVED',
              },
            ],
            services: [
              {
                id: '1',
                name: 'Dinner Date',
                description: 'Elegant companion for dinner and social events',
                price: 400,
                duration: 180,
              },
              {
                id: '2',
                name: 'Overnight Companion',
                description: 'Full night companionship service',
                price: 1200,
                duration: 720,
              },
            ],
            reviews: [
              {
                id: '1',
                rating: 5,
                comment: 'Absolutely wonderful experience. Sophia is elegant, intelligent, and charming.',
                clientName: 'Michael S.',
                createdAt: '2024-01-15T19:00:00Z',
              },
              {
                id: '2',
                rating: 5,
                comment: 'Perfect companion for business dinner. Very professional and sophisticated.',
                clientName: 'Robert K.',
                createdAt: '2024-01-10T20:00:00Z',
              },
            ],
            createdAt: '2023-06-15T12:00:00Z',
          });
          setIsLoading(false);
        }, 1000);
      } catch (error) {
        console.error('Error fetching profile:', error);
        setIsLoading(false);
      }
    };

    if (params.id) {
      fetchProfile();
    }
  }, [params.id]);

  const handleFavorite = () => {
    setIsFavorite(!isFavorite);
    // TODO: Call API to add/remove favorite
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'long',
      year: 'numeric',
    });
  };

  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    if (hours > 0) {
      return `${hours}h${mins > 0 ? ` ${mins}m` : ''}`;
    }
    return `${mins}m`;
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Profile Not Found</h1>
          <p className="text-gray-600 mb-4">The profile you're looking for doesn't exist or is not available.</p>
          <Link href="/">
            <Button>Go Home</Button>
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <Link href="/" className="flex items-center text-gray-600 hover:text-gray-900">
              <ArrowLeft className="mr-2 h-5 w-5" />
              Back to Browse
            </Link>
            <div className="flex items-center space-x-4">
              {user && (
                <Button
                  variant={isFavorite ? 'default' : 'outline'}
                  onClick={handleFavorite}
                >
                  <Heart className={`mr-2 h-4 w-4 ${isFavorite ? 'fill-current' : ''}`} />
                  {isFavorite ? 'Favorited' : 'Add to Favorites'}
                </Button>
              )}
              <Button>Contact</Button>
            </div>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          
          {/* Left Column - Profile Info */}
          <div className="lg:col-span-2 space-y-6">
            
            {/* Cover Image & Profile */}
            <Card>
              <div className="relative h-64 bg-gradient-to-r from-purple-500 to-pink-500 rounded-t-lg">
                {profile.coverImage && (
                  <img
                    src={profile.coverImage}
                    alt="Cover"
                    className="w-full h-full object-cover rounded-t-lg"
                  />
                )}
                <div className="absolute bottom-4 left-4 flex items-end space-x-4">
                  <Avatar className="h-24 w-24 border-4 border-white">
                    <AvatarImage src={profile.profileImage} alt={profile.name} />
                    <AvatarFallback className="text-2xl">
                      {profile.alias?.charAt(0) || profile.name.charAt(0)}
                    </AvatarFallback>
                  </Avatar>
                  <div className="text-white">
                    <h1 className="text-3xl font-bold">{profile.alias || profile.name}</h1>
                    <div className="flex items-center space-x-2 mt-1">
                      <MapPin className="h-4 w-4" />
                      <span>{profile.location}</span>
                    </div>
                  </div>
                </div>
                <div className="absolute top-4 right-4">
                  <Badge 
                    variant={profile.isAvailable ? 'default' : 'secondary'}
                    className="bg-white/90 text-gray-900"
                  >
                    {profile.isAvailable ? 'Available' : 'Unavailable'}
                  </Badge>
                </div>
              </div>
              
              <CardContent className="pt-6">
                <div className="flex items-center space-x-6 mb-6">
                  <div className="flex items-center space-x-1">
                    <Star className="h-5 w-5 fill-current text-yellow-400" />
                    <span className="font-medium">{profile.averageRating}</span>
                    <span className="text-gray-500">({profile.reviewCount} reviews)</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <Heart className="h-5 w-5 text-red-500" />
                    <span className="text-gray-600">{profile.favoriteCount} favorites</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    <CheckCircle className="h-5 w-5 text-green-500" />
                    <span className="text-gray-600">Verified</span>
                  </div>
                </div>
                
                <p className="text-gray-700 leading-relaxed">{profile.description}</p>
              </CardContent>
            </Card>

            {/* Gallery */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <ImageIcon className="mr-2 h-5 w-5" />
                  Gallery
                </CardTitle>
              </CardHeader>
              <CardContent>
                {profile.media.length > 0 ? (
                  <div className="space-y-4">
                    {/* Main Image/Video */}
                    <div className="aspect-video bg-gray-100 rounded-lg overflow-hidden">
                      {profile.media[selectedMediaIndex]?.type === 'IMAGE' ? (
                        <img
                          src={profile.media[selectedMediaIndex]?.fileUrl}
                          alt="Gallery"
                          className="w-full h-full object-cover"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center bg-gray-200">
                          <Video className="h-16 w-16 text-gray-400" />
                        </div>
                      )}
                    </div>
                    
                    {/* Thumbnails */}
                    <div className="grid grid-cols-6 gap-2">
                      {profile.media.map((item, index) => (
                        <button
                          key={item.id}
                          onClick={() => setSelectedMediaIndex(index)}
                          className={`aspect-square rounded-lg overflow-hidden border-2 ${
                            selectedMediaIndex === index ? 'border-blue-500' : 'border-gray-200'
                          }`}
                        >
                          {item.type === 'IMAGE' ? (
                            <img
                              src={item.thumbnailUrl || item.fileUrl}
                              alt="Thumbnail"
                              className="w-full h-full object-cover"
                            />
                          ) : (
                            <div className="w-full h-full flex items-center justify-center bg-gray-100">
                              <Video className="h-6 w-6 text-gray-400" />
                            </div>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <ImageIcon className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p>No gallery images available</p>
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Reviews */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Star className="mr-2 h-5 w-5" />
                  Reviews ({profile.reviewCount})
                </CardTitle>
              </CardHeader>
              <CardContent>
                {profile.reviews.length > 0 ? (
                  <div className="space-y-4">
                    {profile.reviews.map((review) => (
                      <div key={review.id} className="border-b pb-4 last:border-b-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <div className="flex">
                            {[1, 2, 3, 4, 5].map((star) => (
                              <Star
                                key={star}
                                className={`h-4 w-4 ${
                                  star <= review.rating
                                    ? 'text-yellow-400 fill-current'
                                    : 'text-gray-300'
                                }`}
                              />
                            ))}
                          </div>
                          <span className="font-medium text-gray-900">{review.clientName}</span>
                          <span className="text-gray-500 text-sm">
                            {formatDate(review.createdAt)}
                          </span>
                        </div>
                        <p className="text-gray-700">{review.comment}</p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Star className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                    <p>No reviews yet</p>
                  </div>
                )}
              </CardContent>
            </Card>

          </div>

          {/* Right Column - Booking Info */}
          <div className="space-y-6">
            
            {/* Quick Info */}
            <Card>
              <CardHeader>
                <CardTitle>Profile Details</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {profile.age && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Age:</span>
                    <span className="font-medium">{profile.age} years</span>
                  </div>
                )}
                {profile.height && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Height:</span>
                    <span className="font-medium">{profile.height} cm</span>
                  </div>
                )}
                {profile.languages.length > 0 && (
                  <div className="flex justify-between">
                    <span className="text-gray-600">Languages:</span>
                    <span className="font-medium">{profile.languages.join(', ')}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600">Member since:</span>
                  <span className="font-medium">{formatDate(profile.createdAt)}</span>
                </div>
              </CardContent>
            </Card>

            {/* Services & Rates */}
            <Card>
              <CardHeader>
                <CardTitle>Services & Rates</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {profile.hourlyRate && (
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">
                        €{profile.hourlyRate}
                      </div>
                      <div className="text-sm text-blue-700">per hour</div>
                    </div>
                  </div>
                )}
                
                {profile.services.length > 0 && (
                  <div className="space-y-3">
                    {profile.services.map((service) => (
                      <div key={service.id} className="border rounded-lg p-3">
                        <div className="flex justify-between items-start mb-2">
                          <h4 className="font-medium text-gray-900">{service.name}</h4>
                          <div className="text-right">
                            <div className="font-bold text-gray-900">€{service.price}</div>
                            <div className="text-xs text-gray-500">
                              {formatDuration(service.duration)}
                            </div>
                          </div>
                        </div>
                        <p className="text-sm text-gray-600">{service.description}</p>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            {/* Contact */}
            <Card>
              <CardHeader>
                <CardTitle>Contact Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {profile.phone && (
                  <div className="flex items-center space-x-3">
                    <Phone className="h-4 w-4 text-gray-500" />
                    <span className="font-medium">{profile.phone}</span>
                  </div>
                )}
                {profile.website && (
                  <div className="flex items-center space-x-3">
                    <Globe className="h-4 w-4 text-gray-500" />
                    <a 
                      href={profile.website} 
                      target="_blank" 
                      rel="noopener noreferrer"
                      className="text-blue-600 hover:underline"
                    >
                      Visit Website
                    </a>
                  </div>
                )}
                
                <div className="pt-4 border-t">
                  <Button className="w-full" size="lg">
                    <Calendar className="mr-2 h-4 w-4" />
                    Book Now
                  </Button>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>
      </div>
    </div>
  );
}
