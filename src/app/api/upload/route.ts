import { NextRequest } from 'next/server';
import { withAuth } from '@/middleware/auth';
import { processUpload } from '@/lib/upload';
import { prisma } from '@/lib/prisma';
import { MediaType, MediaStatus } from '@prisma/client';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  handleApiError,
  checkRateLimit,
  createRateLimitResponse
} from '@/lib/api';

export const POST = withAuth(async (request) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    // Rate limiting - 10 uploads per hour
    const rateLimit = checkRateLimit(`upload:${user.userId}`, 10, 60 * 60 * 1000);
    if (!rateLimit.allowed) {
      return createRateLimitResponse(rateLimit.resetTime);
    }

    // Check if user has a profile (required for uploads)
    const userProfile = await prisma.profile.findUnique({
      where: { userId: user.userId },
    });

    if (!userProfile) {
      return createErrorResponse('Profile required for uploads', 403);
    }

    // Parse form data
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const type = formData.get('type') as string || 'gallery';

    if (!file) {
      return createErrorResponse('No file provided', 400);
    }

    // Validate file
    if (!file.type.startsWith('image/') && !file.type.startsWith('video/')) {
      return createErrorResponse('Only images and videos are allowed', 400);
    }

    // Process the upload
    const processedFile = await processUpload(file, `profiles/${userProfile.id}`);

    // Save media record to database
    const mediaRecord = await prisma.media.create({
      data: {
        profileId: userProfile.id,
        filename: processedFile.filename,
        originalName: processedFile.originalName,
        fileUrl: processedFile.fileUrl,
        thumbnailUrl: processedFile.thumbnailUrl,
        fileSize: processedFile.fileSize,
        mimeType: processedFile.mimeType,
        type: processedFile.type,
        width: processedFile.width,
        height: processedFile.height,
        duration: processedFile.duration,
        status: MediaStatus.PENDING_APPROVAL, // All uploads need approval
      },
    });

    return createSuccessResponse(
      {
        id: mediaRecord.id,
        filename: mediaRecord.filename,
        fileUrl: mediaRecord.fileUrl,
        thumbnailUrl: mediaRecord.thumbnailUrl,
        type: mediaRecord.type,
        status: mediaRecord.status,
        message: 'File uploaded successfully and is pending approval',
      },
      'Upload successful',
      201
    );

  } catch (error) {
    return handleApiError(error);
  }
});

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
