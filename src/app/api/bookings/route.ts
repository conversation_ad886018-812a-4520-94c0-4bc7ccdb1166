import { NextRequest } from 'next/server';
import { withAuth } from '@/middleware/auth';
import { prisma } from '@/lib/prisma';
import { BookingStatus } from '@/types';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  handleApiError,
  checkRateLimit,
  createRateLimitResponse
} from '@/lib/api';
import { bookingRequestSchema } from '@/lib/validations';

export const POST = withAuth(async (request) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    // Only members can create bookings
    if (user.role !== 'MEMBER') {
      return createErrorResponse('Only members can create bookings', 403);
    }

    // Rate limiting - 5 booking requests per hour
    const rateLimit = checkRateLimit(`booking:${user.userId}`, 5, 60 * 60 * 1000);
    if (!rateLimit.allowed) {
      return createRateLimitResponse(rateLimit.resetTime);
    }

    const body = await request.json();
    const validatedData = bookingRequestSchema.parse(body);

    // Check if profile exists and is available
    const profile = await prisma.profile.findUnique({
      where: { id: validatedData.profileId },
      include: {
        user: {
          select: {
            id: true,
            role: true,
            status: true,
          },
        },
      },
    });

    if (!profile) {
      return createErrorResponse('Profile not found', 404);
    }

    if (profile.status !== 'APPROVED') {
      return createErrorResponse('Profile is not approved for bookings', 403);
    }

    if (!profile.isAvailable) {
      return createErrorResponse('Profile is currently unavailable', 403);
    }

    // Check if user is trying to book their own profile
    if (profile.userId === user.userId) {
      return createErrorResponse('Cannot book your own profile', 400);
    }

    // Check for overlapping bookings
    const startTime = new Date(validatedData.startTime);
    const endTime = new Date(validatedData.endTime);

    const overlappingBooking = await prisma.booking.findFirst({
      where: {
        profileId: validatedData.profileId,
        status: {
          in: ['PENDING', 'CONFIRMED'],
        },
        OR: [
          {
            AND: [
              { startTime: { lte: startTime } },
              { endTime: { gt: startTime } },
            ],
          },
          {
            AND: [
              { startTime: { lt: endTime } },
              { endTime: { gte: endTime } },
            ],
          },
          {
            AND: [
              { startTime: { gte: startTime } },
              { endTime: { lte: endTime } },
            ],
          },
        ],
      },
    });

    if (overlappingBooking) {
      return createErrorResponse('Time slot is already booked', 409);
    }

    // Calculate total price
    const durationHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60);
    const hourlyRate = profile.hourlyRate || 0;
    const totalPrice = Math.round(durationHours * hourlyRate * 100) / 100; // Round to 2 decimal places

    // Create booking
    const booking = await prisma.booking.create({
      data: {
        clientId: user.userId,
        profileId: validatedData.profileId,
        serviceName: validatedData.serviceName,
        startTime,
        endTime,
        totalPrice,
        notes: validatedData.notes,
        status: BookingStatus.PENDING,
      },
      include: {
        profile: {
          select: {
            id: true,
            name: true,
            alias: true,
            profileImage: true,
          },
        },
        client: {
          select: {
            id: true,
            username: true,
            email: true,
          },
        },
      },
    });

    // TODO: Send notification to profile owner
    // await sendBookingNotification(booking);

    return createSuccessResponse(
      {
        id: booking.id,
        profileId: booking.profileId,
        serviceName: booking.serviceName,
        startTime: booking.startTime,
        endTime: booking.endTime,
        totalPrice: booking.totalPrice,
        status: booking.status,
        notes: booking.notes,
        profile: booking.profile,
        createdAt: booking.createdAt,
      },
      'Booking request created successfully',
      201
    );

  } catch (error) {
    return handleApiError(error);
  }
});

export const GET = withAuth(async (request) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    const { searchParams } = new URL(request.url);
    const status = searchParams.get('status');
    const profileId = searchParams.get('profileId');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Build query based on user role
    let where: any = {};

    if (user.role === 'MEMBER') {
      // Members see their own bookings
      where.clientId = user.userId;
    } else if (['ESCORT', 'AGENCY', 'STUDIO'].includes(user.role)) {
      // Service providers see bookings for their profiles
      const userProfiles = await prisma.profile.findMany({
        where: { userId: user.userId },
        select: { id: true },
      });
      
      const profileIds = userProfiles.map(p => p.id);
      where.profileId = { in: profileIds };
    } else if (user.role === 'ADMIN') {
      // Admins see all bookings
      // No additional where clause needed
    } else {
      return createErrorResponse('Unauthorized', 403);
    }

    // Apply filters
    if (status) {
      where.status = status;
    }

    if (profileId) {
      where.profileId = profileId;
    }

    // Get bookings with pagination
    const [bookings, total] = await Promise.all([
      prisma.booking.findMany({
        where,
        include: {
          profile: {
            select: {
              id: true,
              name: true,
              alias: true,
              profileImage: true,
            },
          },
          client: {
            select: {
              id: true,
              username: true,
              email: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.booking.count({ where }),
    ]);

    return createSuccessResponse(
      {
        bookings: bookings.map(booking => ({
          id: booking.id,
          profileId: booking.profileId,
          serviceName: booking.serviceName,
          startTime: booking.startTime,
          endTime: booking.endTime,
          totalPrice: booking.totalPrice,
          status: booking.status,
          notes: booking.notes,
          profile: booking.profile,
          client: user.role === 'ADMIN' || booking.profileId ? booking.client : undefined,
          createdAt: booking.createdAt,
          updatedAt: booking.updatedAt,
        })),
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
      'Bookings retrieved successfully'
    );

  } catch (error) {
    return handleApiError(error);
  }
});

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
