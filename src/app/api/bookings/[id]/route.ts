import { NextRequest } from 'next/server';
import { withAuth } from '@/middleware/auth';
import { prisma } from '@/lib/prisma';
import { BookingStatus } from '@/types';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  handleApiError 
} from '@/lib/api';

interface RouteParams {
  params: {
    id: string;
  };
}

export const GET = withAuth(async (request, { params }: RouteParams) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    const booking = await prisma.booking.findUnique({
      where: { id: params.id },
      include: {
        profile: {
          select: {
            id: true,
            name: true,
            alias: true,
            profileImage: true,
            userId: true,
          },
        },
        client: {
          select: {
            id: true,
            username: true,
            email: true,
          },
        },
      },
    });

    if (!booking) {
      return createErrorResponse('Booking not found', 404);
    }

    // Check permissions
    const canView = 
      user.role === 'ADMIN' ||
      booking.clientId === user.userId ||
      booking.profile.userId === user.userId;

    if (!canView) {
      return createErrorResponse('Permission denied', 403);
    }

    return createSuccessResponse(
      {
        id: booking.id,
        profileId: booking.profileId,
        serviceName: booking.serviceName,
        startTime: booking.startTime,
        endTime: booking.endTime,
        totalPrice: booking.totalPrice,
        status: booking.status,
        notes: booking.notes,
        profile: booking.profile,
        client: booking.client,
        createdAt: booking.createdAt,
        updatedAt: booking.updatedAt,
      },
      'Booking retrieved successfully'
    );

  } catch (error) {
    return handleApiError(error);
  }
});

export const PATCH = withAuth(async (request, { params }: RouteParams) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    const booking = await prisma.booking.findUnique({
      where: { id: params.id },
      include: {
        profile: {
          select: {
            userId: true,
          },
        },
      },
    });

    if (!booking) {
      return createErrorResponse('Booking not found', 404);
    }

    const body = await request.json();
    const { status, notes } = body;

    // Check permissions for status updates
    if (status) {
      const canUpdateStatus = 
        user.role === 'ADMIN' ||
        (booking.profile.userId === user.userId && ['CONFIRMED', 'CANCELLED'].includes(status)) ||
        (booking.clientId === user.userId && status === 'CANCELLED');

      if (!canUpdateStatus) {
        return createErrorResponse('Permission denied for status update', 403);
      }

      // Validate status transitions
      const validTransitions: Record<string, string[]> = {
        'PENDING': ['CONFIRMED', 'CANCELLED'],
        'CONFIRMED': ['COMPLETED', 'CANCELLED'],
        'COMPLETED': [], // Cannot change from completed
        'CANCELLED': [], // Cannot change from cancelled
      };

      if (!validTransitions[booking.status]?.includes(status)) {
        return createErrorResponse(`Cannot change status from ${booking.status} to ${status}`, 400);
      }
    }

    // Check permissions for notes updates
    if (notes !== undefined) {
      const canUpdateNotes = 
        user.role === 'ADMIN' ||
        booking.clientId === user.userId ||
        booking.profile.userId === user.userId;

      if (!canUpdateNotes) {
        return createErrorResponse('Permission denied for notes update', 403);
      }
    }

    // Update booking
    const updatedBooking = await prisma.booking.update({
      where: { id: params.id },
      data: {
        ...(status && { status }),
        ...(notes !== undefined && { notes }),
      },
      include: {
        profile: {
          select: {
            id: true,
            name: true,
            alias: true,
            profileImage: true,
          },
        },
        client: {
          select: {
            id: true,
            username: true,
            email: true,
          },
        },
      },
    });

    // TODO: Send notification about status change
    // if (status) {
    //   await sendBookingStatusNotification(updatedBooking);
    // }

    return createSuccessResponse(
      {
        id: updatedBooking.id,
        profileId: updatedBooking.profileId,
        serviceName: updatedBooking.serviceName,
        startTime: updatedBooking.startTime,
        endTime: updatedBooking.endTime,
        totalPrice: updatedBooking.totalPrice,
        status: updatedBooking.status,
        notes: updatedBooking.notes,
        profile: updatedBooking.profile,
        client: updatedBooking.client,
        createdAt: updatedBooking.createdAt,
        updatedAt: updatedBooking.updatedAt,
      },
      'Booking updated successfully'
    );

  } catch (error) {
    return handleApiError(error);
  }
});

export const DELETE = withAuth(async (request, { params }: RouteParams) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    const booking = await prisma.booking.findUnique({
      where: { id: params.id },
      include: {
        profile: {
          select: {
            userId: true,
          },
        },
      },
    });

    if (!booking) {
      return createErrorResponse('Booking not found', 404);
    }

    // Check permissions - only admin or client can delete
    const canDelete = 
      user.role === 'ADMIN' ||
      booking.clientId === user.userId;

    if (!canDelete) {
      return createErrorResponse('Permission denied', 403);
    }

    // Can only delete pending or cancelled bookings
    if (!['PENDING', 'CANCELLED'].includes(booking.status)) {
      return createErrorResponse('Can only delete pending or cancelled bookings', 400);
    }

    await prisma.booking.delete({
      where: { id: params.id },
    });

    return createSuccessResponse(
      { message: 'Booking deleted successfully' },
      'Booking deleted'
    );

  } catch (error) {
    return handleApiError(error);
  }
});

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, PATCH, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
