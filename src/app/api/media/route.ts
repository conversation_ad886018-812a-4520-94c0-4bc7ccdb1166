import { NextRequest } from 'next/server';
import { withAuth } from '@/middleware/auth';
import { prisma } from '@/lib/prisma';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  handleApiError 
} from '@/lib/api';

export const GET = withAuth(async (request) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    const { searchParams } = new URL(request.url);
    const profileId = searchParams.get('profileId');
    const status = searchParams.get('status');
    const type = searchParams.get('type');

    // If no profileId provided, get user's own profile
    let targetProfileId = profileId;
    if (!targetProfileId) {
      const userProfile = await prisma.profile.findUnique({
        where: { userId: user.userId },
      });
      
      if (!userProfile) {
        return createErrorResponse('Profile not found', 404);
      }
      
      targetProfileId = userProfile.id;
    }

    // Check permissions
    if (profileId && profileId !== targetProfileId) {
      // Only allow viewing other profiles if they're approved and public
      const targetProfile = await prisma.profile.findUnique({
        where: { id: profileId },
      });

      if (!targetProfile || targetProfile.status !== 'APPROVED') {
        return createErrorResponse('Profile not accessible', 403);
      }
    }

    // Build query filters
    const where: any = {
      profileId: targetProfileId,
    };

    if (status) {
      where.status = status;
    }

    if (type) {
      where.type = type;
    }

    // If viewing someone else's profile, only show approved media
    if (profileId && profileId !== targetProfileId) {
      where.status = 'APPROVED';
    }

    // Fetch media
    const media = await prisma.media.findMany({
      where,
      orderBy: {
        createdAt: 'desc',
      },
      include: {
        profile: {
          select: {
            id: true,
            name: true,
            alias: true,
          },
        },
      },
    });

    return createSuccessResponse(
      {
        media: media.map(item => ({
          id: item.id,
          filename: item.filename,
          originalName: item.originalName,
          fileUrl: item.fileUrl,
          thumbnailUrl: item.thumbnailUrl,
          type: item.type,
          status: item.status,
          fileSize: item.fileSize,
          mimeType: item.mimeType,
          width: item.width,
          height: item.height,
          duration: item.duration,
          createdAt: item.createdAt,
          profile: item.profile,
        })),
        total: media.length,
      },
      'Media retrieved successfully'
    );

  } catch (error) {
    return handleApiError(error);
  }
});

export const DELETE = withAuth(async (request) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    const { searchParams } = new URL(request.url);
    const mediaId = searchParams.get('id');

    if (!mediaId) {
      return createErrorResponse('Media ID required', 400);
    }

    // Find the media item
    const mediaItem = await prisma.media.findUnique({
      where: { id: mediaId },
      include: {
        profile: {
          select: {
            userId: true,
          },
        },
      },
    });

    if (!mediaItem) {
      return createErrorResponse('Media not found', 404);
    }

    // Check permissions - only owner or admin can delete
    if (mediaItem.profile.userId !== user.userId && user.role !== 'ADMIN') {
      return createErrorResponse('Permission denied', 403);
    }

    // Delete from database
    await prisma.media.delete({
      where: { id: mediaId },
    });

    // TODO: Delete actual file from storage
    // await deleteFile(mediaItem.fileUrl);
    // if (mediaItem.thumbnailUrl) {
    //   await deleteFile(mediaItem.thumbnailUrl);
    // }

    return createSuccessResponse(
      { message: 'Media deleted successfully' },
      'Media deleted'
    );

  } catch (error) {
    return handleApiError(error);
  }
});

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, DELETE, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
