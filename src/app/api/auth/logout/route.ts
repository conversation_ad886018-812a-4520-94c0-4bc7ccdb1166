import { NextRequest } from 'next/server';
import { withAuth } from '@/middleware/auth';
import { 
  createSuccessResponse, 
  handleApiError 
} from '@/lib/api';

export const POST = withAuth(async (request) => {
  try {
    // In a stateless JWT system, logout is typically handled client-side
    // by removing the token from storage. However, we can log the logout
    // event for security/audit purposes.
    
    const user = request.user;
    console.log(`🔓 User logged out: ${user?.email} (${user?.userId})`);

    // In a production environment, you might want to:
    // 1. Add the token to a blacklist (requires Redis or similar)
    // 2. Log the logout event to an audit table
    // 3. Clear any server-side sessions if using them

    return createSuccessResponse(
      { message: 'Logged out successfully' },
      'Logout successful'
    );

  } catch (error) {
    return handleApiError(error);
  }
});

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
