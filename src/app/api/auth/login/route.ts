import { NextRequest } from 'next/server';
import { loginSchema } from '@/lib/validations';
import { verifyPassword } from '@/lib/password';
import { signToken } from '@/lib/jwt';
import { prisma } from '@/lib/prisma';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  handleApiError, 
  parseRequestBody,
  checkRateLimit,
  createRateLimitResponse
} from '@/lib/api';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || 'unknown';
    const rateLimit = checkRateLimit(`login:${clientIP}`, 5, 15 * 60 * 1000); // 5 attempts per 15 minutes
    
    if (!rateLimit.allowed) {
      return createRateLimitResponse(rateLimit.resetTime);
    }

    // Parse and validate request body
    const body = await parseRequestBody(request);
    const validatedData = loginSchema.parse(body);

    // Find user by email
    const user = await prisma.user.findUnique({
      where: { email: validatedData.email },
      include: {
        profile: true,
      },
    });

    if (!user) {
      return createErrorResponse('Invalid email or password', 401);
    }

    // Check if user is active
    if (user.status === 'SUSPENDED' || user.status === 'BANNED') {
      return createErrorResponse('Account is suspended or banned', 403);
    }

    // Verify password
    const isPasswordValid = await verifyPassword(validatedData.password, user.password);
    if (!isPasswordValid) {
      return createErrorResponse('Invalid email or password', 401);
    }

    // Check email verification
    if (!user.emailVerified) {
      return createErrorResponse('Please verify your email address before logging in', 403);
    }

    // Generate JWT token
    const token = signToken({
      userId: user.id,
      email: user.email,
      role: user.role,
    });

    // Prepare user data for response (exclude sensitive information)
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      status: user.status,
      emailVerified: user.emailVerified,
      profile: user.profile ? {
        id: user.profile.id,
        name: user.profile.name,
        alias: user.profile.alias,
        profileType: user.profile.profileType,
        status: user.profile.status,
        profileImage: user.profile.profileImage,
      } : null,
      createdAt: user.createdAt,
    };

    return createSuccessResponse(
      {
        user: userData,
        token,
      },
      'Login successful'
    );

  } catch (error) {
    return handleApiError(error);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
