import { NextRequest } from 'next/server';
import { registerSchema } from '@/lib/validations';
import { hashPassword } from '@/lib/password';
import { signToken } from '@/lib/jwt';
import { prisma } from '@/lib/prisma';
import { UserRole, UserStatus } from '@prisma/client';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  handleApiError, 
  parseRequestBody,
  checkRateLimit,
  createRateLimitResponse
} from '@/lib/api';

export async function POST(request: NextRequest) {
  try {
    // Rate limiting
    const clientIP = request.ip || 'unknown';
    const rateLimit = checkRateLimit(`register:${clientIP}`, 3, 60 * 60 * 1000); // 3 attempts per hour
    
    if (!rateLimit.allowed) {
      return createRateLimitResponse(rateLimit.resetTime);
    }

    // Parse and validate request body
    const body = await parseRequestBody(request);
    const validatedData = registerSchema.parse(body);

    // Check if user already exists
    const existingUser = await prisma.user.findFirst({
      where: {
        OR: [
          { email: validatedData.email },
          { username: validatedData.username },
        ],
      },
    });

    if (existingUser) {
      if (existingUser.email === validatedData.email) {
        return createErrorResponse('Email address is already registered', 409);
      }
      if (existingUser.username === validatedData.username) {
        return createErrorResponse('Username is already taken', 409);
      }
    }

    // Hash password
    const hashedPassword = await hashPassword(validatedData.password);

    // Create user
    const user = await prisma.user.create({
      data: {
        username: validatedData.username,
        email: validatedData.email,
        password: hashedPassword,
        role: validatedData.role,
        status: UserStatus.PENDING_VERIFICATION,
        emailVerified: false,
      },
    });

    // For non-member roles, create a basic profile
    let profile = null;
    if (validatedData.role !== UserRole.MEMBER) {
      profile = await prisma.profile.create({
        data: {
          userId: user.id,
          profileType: validatedData.role,
          name: validatedData.username, // Default name, can be updated later
          location: '', // Will be filled in profile setup
          description: '',
          isAvailable: false, // Not available until profile is completed and approved
        },
      });
    }

    // Generate JWT token (even though email is not verified yet)
    const token = signToken({
      userId: user.id,
      email: user.email,
      role: user.role,
    });

    // Prepare user data for response
    const userData = {
      id: user.id,
      username: user.username,
      email: user.email,
      role: user.role,
      status: user.status,
      emailVerified: user.emailVerified,
      profile: profile ? {
        id: profile.id,
        name: profile.name,
        profileType: profile.profileType,
        status: profile.status,
      } : null,
      createdAt: user.createdAt,
    };

    // TODO: Send verification email
    // In a production environment, you would send an email verification here
    console.log(`📧 Email verification needed for: ${user.email}`);

    return createSuccessResponse(
      {
        user: userData,
        token,
        message: 'Registration successful. Please check your email for verification instructions.',
      },
      'User registered successfully',
      201
    );

  } catch (error) {
    return handleApiError(error);
  }
}

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
