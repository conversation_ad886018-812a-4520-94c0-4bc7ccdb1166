import { NextRequest } from 'next/server';
import { prisma } from '@/lib/prisma';
import { withAuth } from '@/middleware/auth';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  handleApiError 
} from '@/lib/api';

export const GET = withAuth(async (request) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    // Fetch complete user data
    const userData = await prisma.user.findUnique({
      where: { id: user.userId },
      include: {
        profile: {
          include: {
            media: {
              where: { status: 'APPROVED' },
              take: 5, // Limit to 5 media items
            },
            services: true,
            _count: {
              select: {
                reviews: true,
                favorites: true,
              },
            },
          },
        },
      },
    });

    if (!userData) {
      return createErrorResponse('User not found', 404);
    }

    // Prepare response data (exclude sensitive information)
    const responseData = {
      id: userData.id,
      username: userData.username,
      email: userData.email,
      role: userData.role,
      status: userData.status,
      emailVerified: userData.emailVerified,
      emailVerifiedAt: userData.emailVerifiedAt,
      profile: userData.profile ? {
        id: userData.profile.id,
        name: userData.profile.name,
        alias: userData.profile.alias,
        location: userData.profile.location,
        description: userData.profile.description,
        profileType: userData.profile.profileType,
        status: userData.profile.status,
        age: userData.profile.age,
        gender: userData.profile.gender,
        height: userData.profile.height,
        weight: userData.profile.weight,
        languages: userData.profile.languages,
        hourlyRate: userData.profile.hourlyRate,
        profileImage: userData.profile.profileImage,
        coverImage: userData.profile.coverImage,
        isAvailable: userData.profile.isAvailable,
        phone: userData.profile.phone,
        website: userData.profile.website,
        companyName: userData.profile.companyName,
        media: userData.profile.media,
        services: userData.profile.services,
        _count: userData.profile._count,
        createdAt: userData.profile.createdAt,
        updatedAt: userData.profile.updatedAt,
      } : null,
      createdAt: userData.createdAt,
      updatedAt: userData.updatedAt,
    };

    return createSuccessResponse(responseData, 'User data retrieved successfully');

  } catch (error) {
    return handleApiError(error);
  }
});

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
