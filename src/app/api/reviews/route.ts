import { NextRequest } from 'next/server';
import { withAuth } from '@/middleware/auth';
import { prisma } from '@/lib/prisma';
import { ReviewStatus } from '@prisma/client';
import { 
  createSuccessResponse, 
  createErrorResponse, 
  handleApiError,
  checkRateLimit,
  createRateLimitResponse
} from '@/lib/api';
import { reviewSchema } from '@/lib/validations';

export const POST = withAuth(async (request) => {
  try {
    const user = request.user;
    if (!user) {
      return createErrorResponse('User not found', 404);
    }

    // Only members can create reviews
    if (user.role !== 'MEMBER') {
      return createErrorResponse('Only members can create reviews', 403);
    }

    // Rate limiting - 3 reviews per hour
    const rateLimit = checkRateLimit(`review:${user.userId}`, 3, 60 * 60 * 1000);
    if (!rateLimit.allowed) {
      return createRateLimitResponse(rateLimit.resetTime);
    }

    const body = await request.json();
    const validatedData = reviewSchema.parse(body);

    // Check if profile exists
    const profile = await prisma.profile.findUnique({
      where: { id: validatedData.profileId },
    });

    if (!profile) {
      return createErrorResponse('Profile not found', 404);
    }

    // Check if user has a completed booking with this profile
    const completedBooking = await prisma.booking.findFirst({
      where: {
        clientId: user.userId,
        profileId: validatedData.profileId,
        status: 'COMPLETED',
      },
    });

    if (!completedBooking) {
      return createErrorResponse('You can only review profiles you have booked', 403);
    }

    // Check if user already reviewed this profile
    const existingReview = await prisma.review.findFirst({
      where: {
        clientId: user.userId,
        profileId: validatedData.profileId,
      },
    });

    if (existingReview) {
      return createErrorResponse('You have already reviewed this profile', 409);
    }

    // Create review
    const review = await prisma.review.create({
      data: {
        clientId: user.userId,
        profileId: validatedData.profileId,
        bookingId: completedBooking.id,
        rating: validatedData.rating,
        comment: validatedData.comment,
        status: ReviewStatus.PENDING_APPROVAL, // All reviews need approval
      },
      include: {
        profile: {
          select: {
            id: true,
            name: true,
            alias: true,
          },
        },
        client: {
          select: {
            id: true,
            username: true,
          },
        },
      },
    });

    return createSuccessResponse(
      {
        id: review.id,
        profileId: review.profileId,
        rating: review.rating,
        comment: review.comment,
        status: review.status,
        profile: review.profile,
        client: review.client,
        createdAt: review.createdAt,
      },
      'Review submitted successfully and is pending approval',
      201
    );

  } catch (error) {
    return handleApiError(error);
  }
});

export const GET = withAuth(async (request) => {
  try {
    const user = request.user;
    const { searchParams } = new URL(request.url);
    const profileId = searchParams.get('profileId');
    const status = searchParams.get('status');
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');

    // Build query
    let where: any = {};

    if (profileId) {
      where.profileId = profileId;
      
      // For public profile views, only show approved reviews
      if (!user || (user.role !== 'ADMIN' && user.userId !== profileId)) {
        where.status = ReviewStatus.APPROVED;
      }
    } else if (user) {
      // User-specific queries
      if (user.role === 'MEMBER') {
        // Members see their own reviews
        where.clientId = user.userId;
      } else if (['ESCORT', 'AGENCY', 'STUDIO'].includes(user.role)) {
        // Service providers see reviews for their profiles
        const userProfiles = await prisma.profile.findMany({
          where: { userId: user.userId },
          select: { id: true },
        });
        
        const profileIds = userProfiles.map(p => p.id);
        where.profileId = { in: profileIds };
      } else if (user.role === 'ADMIN') {
        // Admins see all reviews
        // No additional where clause needed
      }
    } else {
      return createErrorResponse('Profile ID required for public access', 400);
    }

    // Apply status filter
    if (status) {
      where.status = status;
    }

    // Get reviews with pagination
    const [reviews, total] = await Promise.all([
      prisma.review.findMany({
        where,
        include: {
          profile: {
            select: {
              id: true,
              name: true,
              alias: true,
              profileImage: true,
            },
          },
          client: {
            select: {
              id: true,
              username: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
        skip: (page - 1) * limit,
        take: limit,
      }),
      prisma.review.count({ where }),
    ]);

    // Calculate average rating for profile
    let averageRating = null;
    if (profileId) {
      const ratingStats = await prisma.review.aggregate({
        where: {
          profileId,
          status: ReviewStatus.APPROVED,
        },
        _avg: {
          rating: true,
        },
        _count: {
          rating: true,
        },
      });

      averageRating = {
        average: ratingStats._avg.rating || 0,
        count: ratingStats._count.rating || 0,
      };
    }

    return createSuccessResponse(
      {
        reviews: reviews.map(review => ({
          id: review.id,
          profileId: review.profileId,
          rating: review.rating,
          comment: review.comment,
          status: review.status,
          profile: review.profile,
          client: review.client,
          createdAt: review.createdAt,
          updatedAt: review.updatedAt,
        })),
        averageRating,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      },
      'Reviews retrieved successfully'
    );

  } catch (error) {
    return handleApiError(error);
  }
});

// Handle OPTIONS request for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
