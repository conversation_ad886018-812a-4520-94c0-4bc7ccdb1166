'use client';

import { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Search, 
  Filter, 
  Heart, 
  Star, 
  MapPin, 
  Eye,
  SlidersHorizontal,
  Grid3X3,
  List
} from 'lucide-react';
import Link from 'next/link';

interface ProfileCard {
  id: string;
  name: string;
  alias?: string;
  description: string;
  location: string;
  profileType: string;
  age?: number;
  hourlyRate?: number;
  profileImage?: string;
  isAvailable: boolean;
  averageRating: number;
  reviewCount: number;
  favoriteCount: number;
  isVerified: boolean;
  languages: string[];
  lastActive: string;
}

export default function BrowsePage() {
  const { user } = useAuth();
  const [profiles, setProfiles] = useState<ProfileCard[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [locationFilter, setLocationFilter] = useState('');
  const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
  const [ageRange, setAgeRange] = useState<[number, number]>([18, 65]);
  const [availableOnly, setAvailableOnly] = useState(false);
  const [verifiedOnly, setVerifiedOnly] = useState(false);
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid');
  const [sortBy, setSortBy] = useState<'rating' | 'price' | 'newest' | 'popular'>('rating');

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setProfiles([
        {
          id: '1',
          name: 'Sophia Elite',
          alias: 'Sophia',
          description: 'Professional companion offering premium services in Berlin.',
          location: 'Berlin, Germany',
          profileType: 'ESCORT',
          age: 26,
          hourlyRate: 300,
          profileImage: '/placeholder-avatar.jpg',
          isAvailable: true,
          averageRating: 4.8,
          reviewCount: 32,
          favoriteCount: 89,
          isVerified: true,
          languages: ['German', 'English', 'French'],
          lastActive: '2024-01-20T14:30:00Z',
        },
        {
          id: '2',
          name: 'Isabella Grace',
          alias: 'Isabella',
          description: 'Elegant and sophisticated companion for discerning gentlemen.',
          location: 'Munich, Germany',
          profileType: 'ESCORT',
          age: 28,
          hourlyRate: 350,
          profileImage: '/placeholder-avatar-2.jpg',
          isAvailable: false,
          averageRating: 4.9,
          reviewCount: 28,
          favoriteCount: 67,
          isVerified: true,
          languages: ['German', 'English', 'Italian'],
          lastActive: '2024-01-19T18:45:00Z',
        },
        {
          id: '3',
          name: 'Victoria Rose',
          alias: 'Victoria',
          description: 'Young and vibrant companion available for all occasions.',
          location: 'Hamburg, Germany',
          profileType: 'ESCORT',
          age: 24,
          hourlyRate: 280,
          profileImage: '/placeholder-avatar-3.jpg',
          isAvailable: true,
          averageRating: 4.6,
          reviewCount: 15,
          favoriteCount: 45,
          isVerified: false,
          languages: ['German', 'English'],
          lastActive: '2024-01-20T10:15:00Z',
        },
        {
          id: '4',
          name: 'Elite Companions Agency',
          description: 'Premium escort agency with a selection of high-class companions.',
          location: 'Frankfurt, Germany',
          profileType: 'AGENCY',
          profileImage: '/placeholder-agency.jpg',
          isAvailable: true,
          averageRating: 4.7,
          reviewCount: 156,
          favoriteCount: 234,
          isVerified: true,
          languages: ['German', 'English', 'French', 'Spanish'],
          lastActive: '2024-01-20T16:20:00Z',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const formatLastActive = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Active now';
    if (diffInHours < 24) return `Active ${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `Active ${diffInDays}d ago`;
  };

  const filteredProfiles = profiles.filter(profile => {
    const matchesSearch = profile.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (profile.alias && profile.alias.toLowerCase().includes(searchTerm.toLowerCase())) ||
                         profile.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesLocation = !locationFilter || profile.location.toLowerCase().includes(locationFilter.toLowerCase());
    const matchesPrice = !profile.hourlyRate || (profile.hourlyRate >= priceRange[0] && profile.hourlyRate <= priceRange[1]);
    const matchesAge = !profile.age || (profile.age >= ageRange[0] && profile.age <= ageRange[1]);
    const matchesAvailable = !availableOnly || profile.isAvailable;
    const matchesVerified = !verifiedOnly || profile.isVerified;
    
    return matchesSearch && matchesLocation && matchesPrice && matchesAge && matchesAvailable && matchesVerified;
  });

  const sortedProfiles = [...filteredProfiles].sort((a, b) => {
    switch (sortBy) {
      case 'rating':
        return b.averageRating - a.averageRating;
      case 'price':
        return (a.hourlyRate || 0) - (b.hourlyRate || 0);
      case 'newest':
        return new Date(b.lastActive).getTime() - new Date(a.lastActive).getTime();
      case 'popular':
        return b.favoriteCount - a.favoriteCount;
      default:
        return 0;
    }
  });

  const ProfileCardComponent = ({ profile }: { profile: ProfileCard }) => (
    <Card className="hover:shadow-lg transition-shadow">
      <div className="relative">
        {profile.profileImage && (
          <div className="aspect-video bg-gray-100 rounded-t-lg overflow-hidden">
            <img
              src={profile.profileImage}
              alt={profile.name}
              className="w-full h-full object-cover"
            />
          </div>
        )}
        <div className="absolute top-2 right-2 flex space-x-2">
          {profile.isVerified && (
            <Badge className="bg-green-100 text-green-800">
              Verified
            </Badge>
          )}
          <Badge variant={profile.isAvailable ? 'default' : 'secondary'}>
            {profile.isAvailable ? 'Available' : 'Unavailable'}
          </Badge>
        </div>
        <div className="absolute bottom-2 left-2">
          <Avatar className="h-12 w-12 border-2 border-white">
            <AvatarImage src={profile.profileImage} alt={profile.name} />
            <AvatarFallback>
              {profile.alias?.charAt(0) || profile.name.charAt(0)}
            </AvatarFallback>
          </Avatar>
        </div>
      </div>
      
      <CardContent className="p-4">
        <div className="flex items-start justify-between mb-2">
          <div>
            <h3 className="font-semibold text-lg text-gray-900">
              {profile.alias || profile.name}
            </h3>
            <div className="flex items-center space-x-1 text-sm text-gray-600">
              <MapPin className="h-3 w-3" />
              <span>{profile.location}</span>
            </div>
          </div>
          {profile.hourlyRate && (
            <div className="text-right">
              <div className="font-bold text-lg text-gray-900">€{profile.hourlyRate}</div>
              <div className="text-xs text-gray-500">per hour</div>
            </div>
          )}
        </div>
        
        <p className="text-sm text-gray-600 mb-3 line-clamp-2">
          {profile.description}
        </p>
        
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-4 text-sm">
            <div className="flex items-center space-x-1">
              <Star className="h-4 w-4 fill-current text-yellow-400" />
              <span className="font-medium">{profile.averageRating}</span>
              <span className="text-gray-500">({profile.reviewCount})</span>
            </div>
            <div className="flex items-center space-x-1">
              <Heart className="h-4 w-4 text-red-500" />
              <span className="text-gray-600">{profile.favoriteCount}</span>
            </div>
          </div>
          <div className="text-xs text-gray-500">
            {formatLastActive(profile.lastActive)}
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <div className="flex flex-wrap gap-1">
            {profile.languages.slice(0, 2).map((lang) => (
              <Badge key={lang} variant="outline" className="text-xs">
                {lang}
              </Badge>
            ))}
            {profile.languages.length > 2 && (
              <Badge variant="outline" className="text-xs">
                +{profile.languages.length - 2}
              </Badge>
            )}
          </div>
          
          <div className="flex space-x-2">
            <Button variant="ghost" size="sm">
              <Heart className="h-4 w-4" />
            </Button>
            <Link href={`/profile/${profile.id}`}>
              <Button size="sm">
                <Eye className="mr-2 h-4 w-4" />
                View
              </Button>
            </Link>
          </div>
        </div>
      </CardContent>
    </Card>
  );

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow-sm">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-3xl font-bold text-gray-900">Browse Profiles</h1>
              <p className="text-gray-600">Discover amazing companions and services</p>
            </div>
            <Link href="/">
              <Button variant="outline">Back to Home</Button>
            </Link>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          
          {/* Filters Sidebar */}
          <div className="lg:col-span-1">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <SlidersHorizontal className="mr-2 h-5 w-5" />
                  Filters
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                
                {/* Search */}
                <div>
                  <Label className="text-sm font-medium">Search</Label>
                  <div className="relative mt-1">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                    <Input
                      placeholder="Search profiles..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                  </div>
                </div>

                {/* Location */}
                <div>
                  <Label className="text-sm font-medium">Location</Label>
                  <Input
                    placeholder="Enter city or region..."
                    value={locationFilter}
                    onChange={(e) => setLocationFilter(e.target.value)}
                    className="mt-1"
                  />
                </div>

                {/* Price Range */}
                <div>
                  <Label className="text-sm font-medium">Price Range (€/hour)</Label>
                  <div className="mt-2 space-y-2">
                    <div className="flex space-x-2">
                      <Input
                        type="number"
                        placeholder="Min"
                        value={priceRange[0]}
                        onChange={(e) => setPriceRange([parseInt(e.target.value) || 0, priceRange[1]])}
                      />
                      <Input
                        type="number"
                        placeholder="Max"
                        value={priceRange[1]}
                        onChange={(e) => setPriceRange([priceRange[0], parseInt(e.target.value) || 1000])}
                      />
                    </div>
                  </div>
                </div>

                {/* Age Range */}
                <div>
                  <Label className="text-sm font-medium">Age Range</Label>
                  <div className="mt-2 space-y-2">
                    <div className="flex space-x-2">
                      <Input
                        type="number"
                        placeholder="Min"
                        value={ageRange[0]}
                        onChange={(e) => setAgeRange([parseInt(e.target.value) || 18, ageRange[1]])}
                      />
                      <Input
                        type="number"
                        placeholder="Max"
                        value={ageRange[1]}
                        onChange={(e) => setAgeRange([ageRange[0], parseInt(e.target.value) || 65])}
                      />
                    </div>
                  </div>
                </div>

                {/* Checkboxes */}
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="available"
                      checked={availableOnly}
                      onChange={(e) => setAvailableOnly(e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="available" className="text-sm">Available only</Label>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="verified"
                      checked={verifiedOnly}
                      onChange={(e) => setVerifiedOnly(e.target.checked)}
                      className="rounded"
                    />
                    <Label htmlFor="verified" className="text-sm">Verified only</Label>
                  </div>
                </div>

                <Button 
                  variant="outline" 
                  className="w-full"
                  onClick={() => {
                    setSearchTerm('');
                    setLocationFilter('');
                    setPriceRange([0, 1000]);
                    setAgeRange([18, 65]);
                    setAvailableOnly(false);
                    setVerifiedOnly(false);
                  }}
                >
                  Clear Filters
                </Button>

              </CardContent>
            </Card>
          </div>

          {/* Results */}
          <div className="lg:col-span-3">
            
            {/* Results Header */}
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-semibold text-gray-900">
                  {sortedProfiles.length} profiles found
                </h2>
                <p className="text-gray-600">Showing results for your search</p>
              </div>
              
              <div className="flex items-center space-x-4">
                {/* Sort */}
                <select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="rating">Highest Rated</option>
                  <option value="price">Lowest Price</option>
                  <option value="newest">Most Recent</option>
                  <option value="popular">Most Popular</option>
                </select>
                
                {/* View Mode */}
                <div className="flex border border-gray-300 rounded-md">
                  <Button
                    variant={viewMode === 'grid' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('grid')}
                    className="rounded-r-none"
                  >
                    <Grid3X3 className="h-4 w-4" />
                  </Button>
                  <Button
                    variant={viewMode === 'list' ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => setViewMode('list')}
                    className="rounded-l-none"
                  >
                    <List className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Results Grid/List */}
            {isLoading ? (
              <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6">
                {[1, 2, 3, 4, 5, 6].map((i) => (
                  <Card key={i} className="animate-pulse">
                    <div className="aspect-video bg-gray-200 rounded-t-lg"></div>
                    <CardContent className="p-4 space-y-3">
                      <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                      <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      <div className="h-3 bg-gray-200 rounded w-full"></div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            ) : sortedProfiles.length === 0 ? (
              <div className="text-center py-12">
                <Search className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No profiles found</h3>
                <p className="text-gray-600">Try adjusting your search criteria or filters.</p>
              </div>
            ) : (
              <div className={viewMode === 'grid' 
                ? 'grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-6'
                : 'space-y-4'
              }>
                {sortedProfiles.map((profile) => (
                  <ProfileCardComponent key={profile.id} profile={profile} />
                ))}
              </div>
            )}

          </div>
        </div>
      </div>
    </div>
  );
}

function Label({ children, htmlFor, className }: { children: React.ReactNode; htmlFor?: string; className?: string }) {
  return (
    <label htmlFor={htmlFor} className={className}>
      {children}
    </label>
  );
}
