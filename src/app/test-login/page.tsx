'use client';

import { useAuth } from '@/contexts/AuthContext';

export default function TestLoginPage() {
  const { user, token, isAuthenticated } = useAuth();

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="max-w-md w-full bg-white rounded-lg shadow-md p-6">
        <h1 className="text-2xl font-bold mb-4">Login Test</h1>
        
        <div className="space-y-4">
          <div>
            <strong>Is Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}
          </div>
          
          <div>
            <strong>Has Token:</strong> {token ? 'Yes' : 'No'}
          </div>
          
          <div>
            <strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'None'}
          </div>
          
          <div>
            <strong>LocalStorage Token:</strong> {typeof window !== 'undefined' ? localStorage.getItem('token') || 'None' : 'N/A'}
          </div>
          
          <div>
            <strong>LocalStorage User:</strong> {typeof window !== 'undefined' ? localStorage.getItem('user') || 'None' : 'N/A'}
          </div>
        </div>
      </div>
    </div>
  );
}
