'use client';

import { useAuth, withAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import EscortManagementWidget from '@/components/dashboard/widgets/EscortManagementWidget';
import ConsolidatedBookingsWidget from '@/components/dashboard/widgets/ConsolidatedBookingsWidget';
import AgencyAnalyticsWidget from '@/components/dashboard/widgets/AgencyAnalyticsWidget';
import AccountSettingsWidget from '@/components/dashboard/widgets/AccountSettingsWidget';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, 
  DollarSign, 
  Users, 
  Calendar, 
  Star,
  AlertTriangle,
  CheckCircle,
  Plus,
  BarChart3,
  Clock
} from 'lucide-react';
import Link from 'next/link';

function AgencyDashboardPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'AGENCY') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">This page is only accessible to agency users.</p>
        </div>
      </div>
    );
  }

  // Mock agency data
  const agencyData = {
    totalEscorts: 8,
    activeEscorts: 6,
    pendingApproval: 1,
    totalRevenue: 45600,
    totalCommission: 9120,
    pendingBookings: 5,
    averageRating: 4.7,
  };

  // Mock quick stats
  const quickStats = [
    {
      title: 'Total Revenue',
      value: `€${agencyData.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      change: '+15.3%',
    },
    {
      title: 'Commission Earned',
      value: `€${agencyData.totalCommission.toLocaleString()}`,
      icon: TrendingUp,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      change: '+12.1%',
    },
    {
      title: 'Active Escorts',
      value: `${agencyData.activeEscorts}/${agencyData.totalEscorts}`,
      icon: Users,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      change: '+2',
    },
    {
      title: 'Pending Bookings',
      value: agencyData.pendingBookings.toString(),
      icon: Calendar,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50',
      change: '+3',
    },
  ];

  return (
    <DashboardLayout 
      title="Agency Dashboard" 
      description={`Welcome back, ${user.profile?.name || user.username}!`}
    >
      <div className="px-4 py-6 sm:px-0">
        
        {/* Status Alerts */}
        <div className="mb-6 space-y-4">
          {!user.emailVerified && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Please verify your email address to receive important notifications.</span>
                <Button variant="outline" size="sm">
                  Resend Verification
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {user.profile && user.profile.status === 'PENDING_APPROVAL' && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Your agency profile is under review. This typically takes 24-48 hours. You'll be notified once approved.
              </AlertDescription>
            </Alert>
          )}

          {agencyData.pendingApproval > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                You have {agencyData.pendingApproval} escort profile{agencyData.pendingApproval > 1 ? 's' : ''} pending approval.
              </AlertDescription>
            </Alert>
          )}

          {agencyData.pendingBookings > 0 && (
            <Alert>
              <Calendar className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>You have {agencyData.pendingBookings} pending booking requests that need attention.</span>
                <Button variant="outline" size="sm">
                  Review Bookings
                </Button>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat) => (
            <Card key={stat.title}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      <Badge variant="outline" className="text-xs text-green-600">
                        {stat.change}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          
          {/* Left Column - Main Widgets */}
          <div className="xl:col-span-2 space-y-6">
            <EscortManagementWidget />
            <ConsolidatedBookingsWidget />
            <AgencyAnalyticsWidget />
          </div>

          {/* Right Column - Secondary Widgets */}
          <div className="space-y-6">
            
            {/* Agency Status Card */}
            {user.profile && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="mr-2 h-5 w-5" />
                    Agency Status
                  </CardTitle>
                  <CardDescription>Your agency information and status</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Agency Status:</span>
                    <Badge 
                      className={
                        user.profile.status === 'APPROVED' 
                          ? 'bg-green-100 text-green-800' 
                          : user.profile.status === 'PENDING_APPROVAL'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }
                    >
                      {user.profile.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Total Escorts:</span>
                    <span className="font-medium">{agencyData.totalEscorts}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Active Escorts:</span>
                    <span className="font-medium">{agencyData.activeEscorts}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Average Rating:</span>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 fill-current text-yellow-400" />
                      <span className="font-medium">{agencyData.averageRating}</span>
                    </div>
                  </div>

                  <div className="pt-3 border-t space-y-2">
                    <Link href="/dashboard/agency/profile">
                      <Button className="w-full">
                        Edit Agency Profile
                      </Button>
                    </Link>
                    <Link href="/dashboard/agency/settings">
                      <Button variant="outline" className="w-full">
                        Agency Settings
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Actions Card */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks and shortcuts</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/dashboard/escorts/add">
                  <Button className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Add New Escort
                  </Button>
                </Link>
                
                <Link href="/dashboard/bookings">
                  <Button variant="outline" className="w-full">
                    <Calendar className="mr-2 h-4 w-4" />
                    Manage Bookings
                  </Button>
                </Link>
                
                <Link href="/dashboard/analytics">
                  <Button variant="outline" className="w-full">
                    <BarChart3 className="mr-2 h-4 w-4" />
                    Detailed Analytics
                  </Button>
                </Link>
                
                <Link href="/dashboard/reports">
                  <Button variant="outline" className="w-full">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    Financial Reports
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Performance Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="mr-2 h-5 w-5" />
                  Performance Summary
                </CardTitle>
                <CardDescription>This month's overview</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Revenue:</span>
                  <span className="font-bold text-lg">€{agencyData.totalRevenue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Commission:</span>
                  <span className="font-medium">€{agencyData.totalCommission.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Commission Rate:</span>
                  <span className="font-medium">
                    {((agencyData.totalCommission / agencyData.totalRevenue) * 100).toFixed(1)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Avg per Escort:</span>
                  <span className="font-medium">
                    €{(agencyData.totalRevenue / agencyData.activeEscorts).toLocaleString()}
                  </span>
                </div>
                <div className="pt-3 border-t">
                  <Link href="/dashboard/reports/detailed">
                    <Button variant="outline" className="w-full">
                      View Detailed Report
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Team Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Team Performance</CardTitle>
                <CardDescription>Top performing escorts this month</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {[
                  { name: 'Sophia', revenue: 15200, bookings: 42 },
                  { name: 'Isabella', revenue: 12800, bookings: 38 },
                  { name: 'Victoria', revenue: 9600, bookings: 28 },
                ].map((escort, index) => (
                  <div key={escort.name} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                        index === 0 ? 'bg-yellow-100 text-yellow-800' :
                        index === 1 ? 'bg-gray-100 text-gray-800' :
                        'bg-orange-100 text-orange-800'
                      }`}>
                        {index + 1}
                      </div>
                      <span className="font-medium text-gray-900">{escort.name}</span>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        €{escort.revenue.toLocaleString()}
                      </div>
                      <div className="text-xs text-gray-500">
                        {escort.bookings} bookings
                      </div>
                    </div>
                  </div>
                ))}
                <div className="pt-3 border-t">
                  <Link href="/dashboard/escorts/performance">
                    <Button variant="outline" className="w-full">
                      View All Performance
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            <AccountSettingsWidget />
          </div>
        </div>

      </div>
    </DashboardLayout>
  );
}

export default withAuth(AgencyDashboardPage, ['AGENCY']);
