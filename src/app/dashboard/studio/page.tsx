'use client';

import { useAuth, withAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import EscortManagementWidget from '@/components/dashboard/widgets/EscortManagementWidget';
import ConsolidatedBookingsWidget from '@/components/dashboard/widgets/ConsolidatedBookingsWidget';
import AgencyAnalyticsWidget from '@/components/dashboard/widgets/AgencyAnalyticsWidget';
import AccountSettingsWidget from '@/components/dashboard/widgets/AccountSettingsWidget';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, 
  DollarSign, 
  Users, 
  Calendar, 
  Star,
  AlertTriangle,
  CheckCircle,
  Plus,
  BarChart3,
  Clock,
  MapPin,
  Home,
  Settings
} from 'lucide-react';
import Link from 'next/link';

function StudioDashboardPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'STUDIO') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">This page is only accessible to studio users.</p>
        </div>
      </div>
    );
  }

  // Mock studio data
  const studioData = {
    totalEscorts: 6,
    activeEscorts: 5,
    pendingApproval: 1,
    totalRevenue: 32400,
    totalCommission: 6480,
    pendingBookings: 3,
    averageRating: 4.8,
    roomsAvailable: 4,
    roomsOccupied: 2,
  };

  // Mock quick stats
  const quickStats = [
    {
      title: 'Total Revenue',
      value: `€${studioData.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      change: '+18.2%',
    },
    {
      title: 'Commission Earned',
      value: `€${studioData.totalCommission.toLocaleString()}`,
      icon: TrendingUp,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      change: '+15.7%',
    },
    {
      title: 'Active Escorts',
      value: `${studioData.activeEscorts}/${studioData.totalEscorts}`,
      icon: Users,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      change: '+1',
    },
    {
      title: 'Room Occupancy',
      value: `${studioData.roomsOccupied}/${studioData.roomsAvailable}`,
      icon: Home,
      color: 'text-orange-500',
      bgColor: 'bg-orange-50',
      change: '50%',
    },
  ];

  return (
    <DashboardLayout 
      title="Studio Dashboard" 
      description={`Welcome back, ${user.profile?.name || user.username}!`}
    >
      <div className="px-4 py-6 sm:px-0">
        
        {/* Status Alerts */}
        <div className="mb-6 space-y-4">
          {!user.emailVerified && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Please verify your email address to receive important notifications.</span>
                <Button variant="outline" size="sm">
                  Resend Verification
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {user.profile && user.profile.status === 'PENDING_APPROVAL' && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Your studio profile is under review. This typically takes 24-48 hours. You'll be notified once approved.
              </AlertDescription>
            </Alert>
          )}

          {studioData.pendingApproval > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription>
                You have {studioData.pendingApproval} escort profile{studioData.pendingApproval > 1 ? 's' : ''} pending approval.
              </AlertDescription>
            </Alert>
          )}

          {studioData.pendingBookings > 0 && (
            <Alert>
              <Calendar className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>You have {studioData.pendingBookings} pending booking requests that need attention.</span>
                <Button variant="outline" size="sm">
                  Review Bookings
                </Button>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat) => (
            <Card key={stat.title}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      <Badge variant="outline" className="text-xs text-green-600">
                        {stat.change}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          
          {/* Left Column - Main Widgets */}
          <div className="xl:col-span-2 space-y-6">
            <EscortManagementWidget />
            <ConsolidatedBookingsWidget />
            <AgencyAnalyticsWidget />
          </div>

          {/* Right Column - Secondary Widgets */}
          <div className="space-y-6">
            
            {/* Studio Status Card */}
            {user.profile && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Home className="mr-2 h-5 w-5" />
                    Studio Status
                  </CardTitle>
                  <CardDescription>Your studio information and status</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Studio Status:</span>
                    <Badge 
                      className={
                        user.profile.status === 'APPROVED' 
                          ? 'bg-green-100 text-green-800' 
                          : user.profile.status === 'PENDING_APPROVAL'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }
                    >
                      {user.profile.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Location:</span>
                    <span className="font-medium">{user.profile.location || 'Not set'}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Total Escorts:</span>
                    <span className="font-medium">{studioData.totalEscorts}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Active Escorts:</span>
                    <span className="font-medium">{studioData.activeEscorts}</span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Room Occupancy:</span>
                    <span className="font-medium">
                      {studioData.roomsOccupied}/{studioData.roomsAvailable} rooms
                    </span>
                  </div>

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Average Rating:</span>
                    <div className="flex items-center space-x-1">
                      <Star className="h-4 w-4 fill-current text-yellow-400" />
                      <span className="font-medium">{studioData.averageRating}</span>
                    </div>
                  </div>

                  <div className="pt-3 border-t space-y-2">
                    <Link href="/dashboard/studio/profile">
                      <Button className="w-full">
                        Edit Studio Profile
                      </Button>
                    </Link>
                    <Link href="/dashboard/studio/rooms">
                      <Button variant="outline" className="w-full">
                        <Home className="mr-2 h-4 w-4" />
                        Manage Rooms
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Studio Facilities */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <MapPin className="mr-2 h-5 w-5" />
                  Studio Facilities
                </CardTitle>
                <CardDescription>Room availability and status</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                {[
                  { name: 'Room 1', status: 'occupied', escort: 'Sophia' },
                  { name: 'Room 2', status: 'available', escort: null },
                  { name: 'Room 3', status: 'occupied', escort: 'Isabella' },
                  { name: 'Room 4', status: 'maintenance', escort: null },
                ].map((room) => (
                  <div key={room.name} className="flex items-center justify-between p-2 border rounded">
                    <span className="font-medium text-gray-900">{room.name}</span>
                    <div className="flex items-center space-x-2">
                      {room.escort && (
                        <span className="text-sm text-gray-600">{room.escort}</span>
                      )}
                      <Badge 
                        variant={
                          room.status === 'available' ? 'default' :
                          room.status === 'occupied' ? 'secondary' : 'destructive'
                        }
                        size="sm"
                      >
                        {room.status}
                      </Badge>
                    </div>
                  </div>
                ))}
                <div className="pt-3 border-t">
                  <Link href="/dashboard/studio/rooms">
                    <Button variant="outline" className="w-full">
                      Manage All Rooms
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            {/* Quick Actions Card */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks and shortcuts</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/dashboard/escorts/add">
                  <Button className="w-full">
                    <Plus className="mr-2 h-4 w-4" />
                    Add New Escort
                  </Button>
                </Link>
                
                <Link href="/dashboard/bookings">
                  <Button variant="outline" className="w-full">
                    <Calendar className="mr-2 h-4 w-4" />
                    Manage Bookings
                  </Button>
                </Link>
                
                <Link href="/dashboard/studio/schedule">
                  <Button variant="outline" className="w-full">
                    <Clock className="mr-2 h-4 w-4" />
                    Room Schedule
                  </Button>
                </Link>
                
                <Link href="/dashboard/analytics">
                  <Button variant="outline" className="w-full">
                    <BarChart3 className="mr-2 h-4 w-4" />
                    Studio Analytics
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Performance Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="mr-2 h-5 w-5" />
                  Performance Summary
                </CardTitle>
                <CardDescription>This month's overview</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Revenue:</span>
                  <span className="font-bold text-lg">€{studioData.totalRevenue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Commission:</span>
                  <span className="font-medium">€{studioData.totalCommission.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Occupancy Rate:</span>
                  <span className="font-medium">
                    {((studioData.roomsOccupied / studioData.roomsAvailable) * 100).toFixed(0)}%
                  </span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Avg per Room:</span>
                  <span className="font-medium">
                    €{(studioData.totalRevenue / studioData.roomsAvailable).toLocaleString()}
                  </span>
                </div>
                <div className="pt-3 border-t">
                  <Link href="/dashboard/reports/detailed">
                    <Button variant="outline" className="w-full">
                      View Detailed Report
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            <AccountSettingsWidget />
          </div>
        </div>

      </div>
    </DashboardLayout>
  );
}

export default withAuth(StudioDashboardPage, ['STUDIO']);
