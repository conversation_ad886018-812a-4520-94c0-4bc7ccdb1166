'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import FavoritesWidget from '@/components/dashboard/widgets/FavoritesWidget';
import BookingHistoryWidget from '@/components/dashboard/widgets/BookingHistoryWidget';
import AccountSettingsWidget from '@/components/dashboard/widgets/AccountSettingsWidget';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  TrendingUp,
  Heart,
  Calendar,
  Star,
  Eye,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';
import Link from 'next/link';

function DashboardPage() {
  const { user } = useAuth();
  const router = useRouter();

  if (!user) {
    return null;
  }

  // Redirect to role-specific dashboards
  useEffect(() => {
    if (user.role === 'ESCORT') {
      router.push('/dashboard/escort');
    } else if (user.role === 'AGENCY') {
      router.push('/dashboard/agency');
    } else if (user.role === 'STUDIO') {
      router.push('/dashboard/studio');
    } else if (user.role === 'ADMIN') {
      router.push('/admin');
    }
    // MEMBER stays on this page
  }, [user.role, router]);

  // Mock statistics for member dashboard
  const stats = [
    {
      title: 'Favorites',
      value: '12',
      icon: Heart,
      color: 'text-red-500',
      bgColor: 'bg-red-50',
    },
    {
      title: 'Bookings',
      value: '8',
      icon: Calendar,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
    },
    {
      title: 'Reviews Given',
      value: '6',
      icon: Star,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50',
    },
    {
      title: 'Profile Views',
      value: '24',
      icon: Eye,
      color: 'text-green-500',
      bgColor: 'bg-green-50',
    },
  ];

  return (
    <DashboardLayout
      title="Dashboard"
      description={`Welcome back, ${user.username}!`}
    >
      <div className="px-4 py-6 sm:px-0">

        {/* Status Alerts */}
        <div className="mb-6 space-y-4">
          {!user.emailVerified && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Please verify your email address to access all features.</span>
                <Button variant="outline" size="sm">
                  Resend Verification
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {user.profile && user.profile.status === 'PENDING_APPROVAL' && (
            <Alert>
              <CheckCircle className="h-4 w-4" />
              <AlertDescription>
                Your profile is under review. This typically takes 24-48 hours.
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Statistics Cards */}
        {user.role === 'MEMBER' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            {stats.map((stat) => (
              <Card key={stat.title}>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                      <stat.icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">

          {/* Left Column - Main Widgets */}
          <div className="lg:col-span-2 space-y-6">

            {user.role === 'MEMBER' && (
              <>
                <FavoritesWidget />
                <BookingHistoryWidget />
              </>
            )}

            {/* Welcome Card for new users */}
            {user.role === 'MEMBER' && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="mr-2 h-5 w-5" />
                    Getting Started
                  </CardTitle>
                  <CardDescription>
                    Explore the platform and discover amazing profiles
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Link href="/browse">
                      <Button className="w-full">
                        Browse Profiles
                      </Button>
                    </Link>
                    <Link href="/search">
                      <Button variant="outline" className="w-full">
                        Advanced Search
                      </Button>
                    </Link>
                  </div>
                  <div className="text-sm text-gray-600">
                    <p>• Browse verified escort profiles</p>
                    <p>• Use advanced filters to find exactly what you're looking for</p>
                    <p>• Save profiles to your favorites for quick access</p>
                    <p>• Book services securely through our platform</p>
                  </div>
                </CardContent>
              </Card>
            )}

          </div>

          {/* Right Column - Settings and Info */}
          <div className="space-y-6">
            <AccountSettingsWidget />

            {/* Profile Status Card */}
            {user.profile && (
              <Card>
                <CardHeader>
                  <CardTitle>Profile Status</CardTitle>
                  <CardDescription>Your profile information</CardDescription>
                </CardHeader>
                <CardContent className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Profile Type:</span>
                    <Badge variant="outline">{user.profile.profileType}</Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Status:</span>
                    <Badge
                      className={
                        user.profile.status === 'APPROVED'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-yellow-100 text-yellow-800'
                      }
                    >
                      {user.profile.status}
                    </Badge>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Available:</span>
                    <Badge
                      variant={user.profile.isAvailable ? 'default' : 'secondary'}
                    >
                      {user.profile.isAvailable ? 'Yes' : 'No'}
                    </Badge>
                  </div>
                  <div className="pt-3 border-t">
                    <Link href="/dashboard/profile">
                      <Button variant="outline" className="w-full">
                        Edit Profile
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}

export default withAuth(DashboardPage);
