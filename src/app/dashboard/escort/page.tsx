'use client';

import { useAuth, withAuth } from '@/contexts/AuthContext';
import DashboardLayout from '@/components/dashboard/DashboardLayout';
import ProfileStatsWidget from '@/components/dashboard/widgets/ProfileStatsWidget';
import GalleryManagerWidget from '@/components/dashboard/widgets/GalleryManagerWidget';
import EscortBookingsWidget from '@/components/dashboard/widgets/EscortBookingsWidget';
import AvailabilityWidget from '@/components/dashboard/widgets/AvailabilityWidget';
import AccountSettingsWidget from '@/components/dashboard/widgets/AccountSettingsWidget';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  TrendingUp, 
  DollarSign, 
  Calendar, 
  Star, 
  Eye,
  AlertTriangle,
  CheckCircle,
  Edit,
  Camera,
  Clock
} from 'lucide-react';
import Link from 'next/link';

function EscortDashboardPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'ESCORT') {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Access Denied</h1>
          <p className="text-gray-600">This page is only accessible to escort users.</p>
        </div>
      </div>
    );
  }

  // Mock earnings data
  const earningsData = {
    today: 0,
    thisWeek: 1200,
    thisMonth: 4800,
    total: 15600,
  };

  // Mock quick stats
  const quickStats = [
    {
      title: 'Today\'s Earnings',
      value: `€${earningsData.today}`,
      icon: DollarSign,
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      change: '+0%',
    },
    {
      title: 'This Week',
      value: `€${earningsData.thisWeek}`,
      icon: TrendingUp,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      change: '+15%',
    },
    {
      title: 'Pending Bookings',
      value: '3',
      icon: Calendar,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50',
      change: '+2',
    },
    {
      title: 'Average Rating',
      value: '4.8',
      icon: Star,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      change: '+0.2',
    },
  ];

  return (
    <DashboardLayout 
      title="Escort Dashboard" 
      description={`Welcome back, ${user.profile?.alias || user.username}!`}
    >
      <div className="px-4 py-6 sm:px-0">
        
        {/* Status Alerts */}
        <div className="mb-6 space-y-4">
          {!user.emailVerified && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Please verify your email address to receive booking notifications.</span>
                <Button variant="outline" size="sm">
                  Resend Verification
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {user.profile && user.profile.status === 'PENDING_APPROVAL' && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription>
                Your profile is under review. This typically takes 24-48 hours. You'll be notified once approved.
              </AlertDescription>
            </Alert>
          )}

          {user.profile && user.profile.status === 'APPROVED' && !user.profile.isAvailable && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>Your profile is set to unavailable. Clients cannot book your services.</span>
                <Button variant="outline" size="sm">
                  Set Available
                </Button>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat) => (
            <Card key={stat.title}>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                    <stat.icon className={`h-6 w-6 ${stat.color}`} />
                  </div>
                  <div className="ml-4">
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <div className="flex items-center space-x-2">
                      <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                      <Badge variant="outline" className="text-xs text-green-600">
                        {stat.change}
                      </Badge>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-3 gap-6">
          
          {/* Left Column - Main Widgets */}
          <div className="xl:col-span-2 space-y-6">
            <ProfileStatsWidget />
            <EscortBookingsWidget />
            <AvailabilityWidget />
          </div>

          {/* Right Column - Secondary Widgets */}
          <div className="space-y-6">
            
            {/* Profile Status Card */}
            {user.profile && (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Edit className="mr-2 h-5 w-5" />
                    Profile Status
                  </CardTitle>
                  <CardDescription>Your profile information and status</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Profile Status:</span>
                    <Badge 
                      className={
                        user.profile.status === 'APPROVED' 
                          ? 'bg-green-100 text-green-800' 
                          : user.profile.status === 'PENDING_APPROVAL'
                          ? 'bg-yellow-100 text-yellow-800'
                          : 'bg-red-100 text-red-800'
                      }
                    >
                      {user.profile.status.replace('_', ' ')}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Availability:</span>
                    <Badge 
                      variant={user.profile.isAvailable ? 'default' : 'secondary'}
                    >
                      {user.profile.isAvailable ? 'Available' : 'Unavailable'}
                    </Badge>
                  </div>

                  {user.profile.hourlyRate && (
                    <div className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">Hourly Rate:</span>
                      <span className="font-medium">€{user.profile.hourlyRate}</span>
                    </div>
                  )}

                  <div className="flex items-center justify-between">
                    <span className="text-sm text-gray-600">Profile Views:</span>
                    <span className="font-medium">1,247</span>
                  </div>

                  <div className="pt-3 border-t space-y-2">
                    <Link href="/dashboard/profile/edit">
                      <Button className="w-full">
                        <Edit className="mr-2 h-4 w-4" />
                        Edit Profile
                      </Button>
                    </Link>
                    <Link href="/profile/preview">
                      <Button variant="outline" className="w-full">
                        <Eye className="mr-2 h-4 w-4" />
                        Preview Profile
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Actions Card */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common tasks and shortcuts</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/dashboard/gallery">
                  <Button variant="outline" className="w-full">
                    <Camera className="mr-2 h-4 w-4" />
                    Manage Gallery
                  </Button>
                </Link>
                
                <Link href="/dashboard/services">
                  <Button variant="outline" className="w-full">
                    <Edit className="mr-2 h-4 w-4" />
                    Edit Services
                  </Button>
                </Link>
                
                <Link href="/dashboard/calendar">
                  <Button variant="outline" className="w-full">
                    <Calendar className="mr-2 h-4 w-4" />
                    Full Calendar
                  </Button>
                </Link>
                
                <Link href="/dashboard/earnings">
                  <Button variant="outline" className="w-full">
                    <DollarSign className="mr-2 h-4 w-4" />
                    Earnings Report
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Earnings Summary */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <DollarSign className="mr-2 h-5 w-5" />
                  Earnings Summary
                </CardTitle>
                <CardDescription>Your income overview</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">This Month:</span>
                  <span className="font-bold text-lg">€{earningsData.thisMonth}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">This Week:</span>
                  <span className="font-medium">€{earningsData.thisWeek}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Earned:</span>
                  <span className="font-medium">€{earningsData.total}</span>
                </div>
                <div className="pt-3 border-t">
                  <Link href="/dashboard/earnings">
                    <Button variant="outline" className="w-full">
                      View Detailed Report
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

            <AccountSettingsWidget />
          </div>
        </div>

        {/* Gallery Preview */}
        <div className="mt-8">
          <GalleryManagerWidget />
        </div>

      </div>
    </DashboardLayout>
  );
}

export default withAuth(EscortDashboardPage, ['ESCORT']);
