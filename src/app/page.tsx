import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

export default function Home() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-slate-100">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-bold text-slate-900">TheGND</h1>
              <span className="text-sm text-slate-500">Premium Platform</span>
            </div>
            <nav className="flex items-center space-x-4">
              <Link href="/auth/login">
                <Button variant="ghost">Login</Button>
              </Link>
              <Link href="/auth/register">
                <Button>Get Started</Button>
              </Link>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl md:text-6xl font-bold text-slate-900 mb-6">
            Premium Escort Platform
          </h2>
          <p className="text-xl text-slate-600 mb-8 max-w-2xl mx-auto">
            A secure, professional platform connecting clients with verified escort services.
            Built with privacy, security, and user experience in mind.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/register">
              <Button size="lg" className="w-full sm:w-auto">
                Join as Member
              </Button>
            </Link>
            <Link href="/auth/register">
              <Button size="lg" variant="outline" className="w-full sm:w-auto">
                Register as Provider
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-white">
        <div className="container mx-auto px-4">
          <h3 className="text-3xl font-bold text-center text-slate-900 mb-12">
            Platform Features
          </h3>
          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>For Members</CardTitle>
                <CardDescription>
                  Browse verified profiles and book services
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-slate-600">
                  <li>• Browse verified escort profiles</li>
                  <li>• Advanced search and filtering</li>
                  <li>• Secure booking system</li>
                  <li>• Favorites and reviews</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>For Escorts</CardTitle>
                <CardDescription>
                  Create your profile and manage bookings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-slate-600">
                  <li>• Professional profile creation</li>
                  <li>• Photo and video galleries</li>
                  <li>• Booking management</li>
                  <li>• Earnings tracking</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>For Agencies</CardTitle>
                <CardDescription>
                  Manage multiple escorts and bookings
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2 text-sm text-slate-600">
                  <li>• Multi-escort management</li>
                  <li>• Centralized booking system</li>
                  <li>• Analytics and reporting</li>
                  <li>• Team collaboration tools</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-slate-900 text-white py-8">
        <div className="container mx-auto px-4 text-center">
          <p className="text-slate-400">
            © 2024 TheGND. All rights reserved. Built with Next.js and Shadcn/ui.
          </p>
        </div>
      </footer>
    </div>
  );
}
