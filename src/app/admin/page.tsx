'use client';

import { useAuth, withAuth } from '@/contexts/AuthContext';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Users, 
  UserCheck, 
  FileText, 
  DollarSign,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Eye,
  Star,
  Calendar,
  Shield
} from 'lucide-react';
import Link from 'next/link';

function AdminDashboardPage() {
  const { user } = useAuth();

  if (!user || user.role !== 'ADMIN') {
    return null;
  }

  // Mock admin statistics
  const stats = {
    totalUsers: 1247,
    newUsersToday: 23,
    totalProfiles: 156,
    pendingApprovals: 8,
    totalBookings: 2341,
    totalRevenue: 234567,
    pendingReviews: 12,
    systemAlerts: 3,
  };

  const quickStats = [
    {
      title: 'Total Users',
      value: stats.totalUsers.toLocaleString(),
      icon: Users,
      color: 'text-blue-500',
      bgColor: 'bg-blue-50',
      change: `+${stats.newUsersToday} today`,
      href: '/admin/users',
    },
    {
      title: 'Pending Approvals',
      value: stats.pendingApprovals.toString(),
      icon: UserCheck,
      color: 'text-yellow-500',
      bgColor: 'bg-yellow-50',
      change: 'Needs attention',
      href: '/admin/moderation',
      urgent: true,
    },
    {
      title: 'Total Revenue',
      value: `€${stats.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-green-500',
      bgColor: 'bg-green-50',
      change: '+15.3% this month',
      href: '/admin/analytics',
    },
    {
      title: 'Active Profiles',
      value: stats.totalProfiles.toString(),
      icon: FileText,
      color: 'text-purple-500',
      bgColor: 'bg-purple-50',
      change: '+12 this week',
      href: '/admin/users',
    },
  ];

  const recentActivity = [
    {
      id: '1',
      type: 'user_registration',
      message: 'New escort registration: Sophia Elite',
      time: '2 minutes ago',
      status: 'pending',
    },
    {
      id: '2',
      type: 'profile_approval',
      message: 'Profile approved: Isabella Grace',
      time: '15 minutes ago',
      status: 'approved',
    },
    {
      id: '3',
      type: 'booking_completed',
      message: 'Booking completed: €450 commission earned',
      time: '1 hour ago',
      status: 'completed',
    },
    {
      id: '4',
      type: 'review_submitted',
      message: 'New review submitted for Victoria Rose',
      time: '2 hours ago',
      status: 'pending',
    },
    {
      id: '5',
      type: 'security_alert',
      message: 'Multiple failed login attempts detected',
      time: '3 hours ago',
      status: 'alert',
    },
  ];

  const getActivityIcon = (type: string) => {
    switch (type) {
      case 'user_registration':
        return <Users className="h-4 w-4" />;
      case 'profile_approval':
        return <CheckCircle className="h-4 w-4" />;
      case 'booking_completed':
        return <DollarSign className="h-4 w-4" />;
      case 'review_submitted':
        return <Star className="h-4 w-4" />;
      case 'security_alert':
        return <Shield className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getActivityColor = (status: string) => {
    switch (status) {
      case 'approved':
      case 'completed':
        return 'text-green-600';
      case 'pending':
        return 'text-yellow-600';
      case 'alert':
        return 'text-red-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <AdminLayout 
      title="Admin Dashboard" 
      description="Platform overview and management"
    >
      <div className="px-4 py-6 sm:px-0">
        
        {/* System Alerts */}
        <div className="mb-6 space-y-4">
          {stats.systemAlerts > 0 && (
            <Alert>
              <AlertTriangle className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>You have {stats.systemAlerts} system alerts that require attention.</span>
                <Button variant="outline" size="sm">
                  View Alerts
                </Button>
              </AlertDescription>
            </Alert>
          )}

          {stats.pendingApprovals > 0 && (
            <Alert>
              <Clock className="h-4 w-4" />
              <AlertDescription className="flex items-center justify-between">
                <span>{stats.pendingApprovals} profiles are waiting for approval.</span>
                <Link href="/admin/moderation">
                  <Button variant="outline" size="sm">
                    Review Now
                  </Button>
                </Link>
              </AlertDescription>
            </Alert>
          )}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {quickStats.map((stat) => (
            <Link key={stat.title} href={stat.href}>
              <Card className={`hover:shadow-md transition-shadow cursor-pointer ${
                stat.urgent ? 'border-yellow-200 bg-yellow-50' : ''
              }`}>
                <CardContent className="p-6">
                  <div className="flex items-center">
                    <div className={`p-2 rounded-lg ${stat.bgColor}`}>
                      <stat.icon className={`h-6 w-6 ${stat.color}`} />
                    </div>
                    <div className="ml-4">
                      <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                      <div className="flex items-center space-x-2">
                        <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                        {stat.urgent && (
                          <Badge variant="destructive" size="sm">
                            Urgent
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-gray-500 mt-1">{stat.change}</p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {/* Main Dashboard Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          
          {/* Left Column - Recent Activity */}
          <div className="lg:col-span-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="mr-2 h-5 w-5" />
                  Recent Activity
                </CardTitle>
                <CardDescription>Latest platform activities and events</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {recentActivity.map((activity) => (
                    <div key={activity.id} className="flex items-start space-x-3 p-3 border rounded-lg hover:bg-gray-50">
                      <div className={`p-1 rounded ${getActivityColor(activity.status)}`}>
                        {getActivityIcon(activity.type)}
                      </div>
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">
                          {activity.message}
                        </p>
                        <p className="text-xs text-gray-500">{activity.time}</p>
                      </div>
                      <Badge 
                        variant={
                          activity.status === 'approved' || activity.status === 'completed' ? 'default' :
                          activity.status === 'pending' ? 'secondary' : 'destructive'
                        }
                        size="sm"
                      >
                        {activity.status}
                      </Badge>
                    </div>
                  ))}
                </div>
                <div className="mt-4 pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    View All Activity
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Quick Actions & Summary */}
          <div className="space-y-6">
            
            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
                <CardDescription>Common administrative tasks</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <Link href="/admin/moderation">
                  <Button className="w-full" variant={stats.pendingApprovals > 0 ? 'default' : 'outline'}>
                    <UserCheck className="mr-2 h-4 w-4" />
                    Review Approvals
                    {stats.pendingApprovals > 0 && (
                      <Badge variant="secondary" className="ml-2">
                        {stats.pendingApprovals}
                      </Badge>
                    )}
                  </Button>
                </Link>
                
                <Link href="/admin/users">
                  <Button variant="outline" className="w-full">
                    <Users className="mr-2 h-4 w-4" />
                    Manage Users
                  </Button>
                </Link>
                
                <Link href="/admin/analytics">
                  <Button variant="outline" className="w-full">
                    <TrendingUp className="mr-2 h-4 w-4" />
                    View Analytics
                  </Button>
                </Link>
                
                <Link href="/admin/content">
                  <Button variant="outline" className="w-full">
                    <FileText className="mr-2 h-4 w-4" />
                    Content Management
                  </Button>
                </Link>
              </CardContent>
            </Card>

            {/* Platform Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Platform Summary</CardTitle>
                <CardDescription>Key metrics overview</CardDescription>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Users:</span>
                  <span className="font-medium">{stats.totalUsers.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Active Profiles:</span>
                  <span className="font-medium">{stats.totalProfiles}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Total Bookings:</span>
                  <span className="font-medium">{stats.totalBookings.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Platform Revenue:</span>
                  <span className="font-medium">€{stats.totalRevenue.toLocaleString()}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-sm text-gray-600">Pending Reviews:</span>
                  <span className="font-medium">{stats.pendingReviews}</span>
                </div>
              </CardContent>
            </Card>

            {/* System Status */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Shield className="mr-2 h-5 w-5" />
                  System Status
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Database:</span>
                  <Badge className="bg-green-100 text-green-800">Healthy</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">API Status:</span>
                  <Badge className="bg-green-100 text-green-800">Online</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Storage:</span>
                  <Badge className="bg-yellow-100 text-yellow-800">85% Used</Badge>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Security:</span>
                  <Badge className="bg-green-100 text-green-800">Secure</Badge>
                </div>
                <div className="pt-3 border-t">
                  <Link href="/admin/security">
                    <Button variant="outline" className="w-full">
                      View Security Details
                    </Button>
                  </Link>
                </div>
              </CardContent>
            </Card>

          </div>
        </div>

      </div>
    </AdminLayout>
  );
}

export default withAuth(AdminDashboardPage, ['ADMIN']);
