'use client';

import { useState, useEffect } from 'react';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { 
  UserCheck, 
  Check, 
  X, 
  Clock,
  Eye,
  AlertTriangle,
  Image as ImageIcon,
  Video,
  User,
  Star
} from 'lucide-react';

interface ModerationItem {
  id: string;
  type: 'profile' | 'media' | 'review';
  itemId: string;
  submittedBy: string;
  submittedByEmail: string;
  submittedAt: string;
  status: 'pending' | 'approved' | 'rejected';
  moderatedBy?: string;
  moderatedAt?: string;
  rejectionNote?: string;
  // Profile specific
  profileName?: string;
  profileDescription?: string;
  profileImage?: string;
  // Media specific
  mediaUrl?: string;
  mediaType?: 'IMAGE' | 'VIDEO';
  mediaFilename?: string;
  // Review specific
  reviewRating?: number;
  reviewComment?: string;
  reviewTargetProfile?: string;
}

function AdminModerationPage() {
  const { user } = useAuth();
  const [items, setItems] = useState<ModerationItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'profile' | 'media' | 'review'>('all');
  const [rejectionNote, setRejectionNote] = useState<{ [key: string]: string }>({});

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setItems([
        {
          id: '1',
          type: 'profile',
          itemId: 'profile-123',
          submittedBy: 'Victoria Rose',
          submittedByEmail: '<EMAIL>',
          submittedAt: '2024-01-20T10:30:00Z',
          status: 'pending',
          profileName: 'Victoria Rose',
          profileDescription: 'Professional companion offering premium services in Berlin.',
          profileImage: '/placeholder-avatar.jpg',
        },
        {
          id: '2',
          type: 'media',
          itemId: 'media-456',
          submittedBy: 'Sophia Elite',
          submittedByEmail: '<EMAIL>',
          submittedAt: '2024-01-20T09:15:00Z',
          status: 'pending',
          mediaUrl: '/placeholder-image.jpg',
          mediaType: 'IMAGE',
          mediaFilename: 'gallery-photo-3.jpg',
        },
        {
          id: '3',
          type: 'review',
          itemId: 'review-789',
          submittedBy: 'John D.',
          submittedByEmail: '<EMAIL>',
          submittedAt: '2024-01-20T08:45:00Z',
          status: 'pending',
          reviewRating: 5,
          reviewComment: 'Excellent service, very professional and punctual.',
          reviewTargetProfile: 'Isabella Grace',
        },
        {
          id: '4',
          type: 'media',
          itemId: 'media-321',
          submittedBy: 'Isabella Grace',
          submittedByEmail: '<EMAIL>',
          submittedAt: '2024-01-19T16:20:00Z',
          status: 'rejected',
          moderatedBy: 'admin',
          moderatedAt: '2024-01-19T18:30:00Z',
          rejectionNote: 'Image quality too low. Please upload in higher resolution.',
          mediaUrl: '/placeholder-image-2.jpg',
          mediaType: 'IMAGE',
          mediaFilename: 'profile-photo-new.jpg',
        },
        {
          id: '5',
          type: 'profile',
          itemId: 'profile-654',
          submittedBy: 'Elena Luxury',
          submittedByEmail: '<EMAIL>',
          submittedAt: '2024-01-18T14:10:00Z',
          status: 'approved',
          moderatedBy: 'admin',
          moderatedAt: '2024-01-18T15:45:00Z',
          profileName: 'Elena Luxury',
          profileDescription: 'High-class escort available for dinner dates and social events.',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'profile':
        return <User className="h-4 w-4" />;
      case 'media':
        return <ImageIcon className="h-4 w-4" />;
      case 'review':
        return <Star className="h-4 w-4" />;
      default:
        return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'profile':
        return 'bg-blue-100 text-blue-800';
      case 'media':
        return 'bg-purple-100 text-purple-800';
      case 'review':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'approved':
        return 'bg-green-100 text-green-800';
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'rejected':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleApprove = (itemId: string) => {
    setItems(items.map(item => 
      item.id === itemId 
        ? { 
            ...item, 
            status: 'approved' as const, 
            moderatedBy: user?.username,
            moderatedAt: new Date().toISOString()
          }
        : item
    ));
  };

  const handleReject = (itemId: string) => {
    const note = rejectionNote[itemId];
    if (!note) {
      alert('Please provide a rejection note');
      return;
    }

    setItems(items.map(item => 
      item.id === itemId 
        ? { 
            ...item, 
            status: 'rejected' as const, 
            moderatedBy: user?.username,
            moderatedAt: new Date().toISOString(),
            rejectionNote: note
          }
        : item
    ));
    
    // Clear the rejection note
    setRejectionNote(prev => ({ ...prev, [itemId]: '' }));
  };

  const filteredItems = items.filter(item => 
    filter === 'all' || item.type === filter
  );

  const pendingItems = items.filter(item => item.status === 'pending');
  const approvedItems = items.filter(item => item.status === 'approved');
  const rejectedItems = items.filter(item => item.status === 'rejected');

  if (!user || user.role !== 'ADMIN') {
    return null;
  }

  return (
    <AdminLayout 
      title="Moderation Queue" 
      description="Review and moderate user-submitted content"
    >
      <div className="px-4 py-6 sm:px-0">
        
        {/* Moderation Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-yellow-50">
                  <Clock className="h-6 w-6 text-yellow-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">{pendingItems.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-green-50">
                  <Check className="h-6 w-6 text-green-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Approved</p>
                  <p className="text-2xl font-bold text-gray-900">{approvedItems.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-red-50">
                  <X className="h-6 w-6 text-red-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Rejected</p>
                  <p className="text-2xl font-bold text-gray-900">{rejectedItems.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-blue-50">
                  <UserCheck className="h-6 w-6 text-blue-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total</p>
                  <p className="text-2xl font-bold text-gray-900">{items.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Moderation Queue */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  <UserCheck className="mr-2 h-5 w-5" />
                  Moderation Queue
                  {pendingItems.length > 0 && (
                    <Badge variant="destructive" className="ml-2">
                      {pendingItems.length} pending
                    </Badge>
                  )}
                </CardTitle>
                <CardDescription>
                  {filteredItems.length} items to review
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                {(['all', 'profile', 'media', 'review'] as const).map((filterType) => (
                  <Button
                    key={filterType}
                    variant={filter === filterType ? 'default' : 'outline'}
                    size="sm"
                    onClick={() => setFilter(filterType)}
                  >
                    {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
                  </Button>
                ))}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse border rounded-lg p-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gray-200 rounded"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredItems.length === 0 ? (
              <div className="text-center py-8">
                <UserCheck className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No items to moderate</h3>
                <p className="mt-1 text-sm text-gray-500">
                  All content has been reviewed.
                </p>
              </div>
            ) : (
              <div className="space-y-6">
                {filteredItems.map((item) => (
                  <div key={item.id} className="border rounded-lg p-6">
                    <div className="flex items-start space-x-4">
                      
                      {/* Content Preview */}
                      <div className="flex-shrink-0">
                        {item.type === 'profile' && (
                          <Avatar className="h-16 w-16">
                            <AvatarImage src={item.profileImage} alt={item.profileName} />
                            <AvatarFallback>{item.profileName?.charAt(0)}</AvatarFallback>
                          </Avatar>
                        )}
                        {item.type === 'media' && (
                          <div className="w-16 h-16 bg-gray-100 rounded-lg flex items-center justify-center">
                            {item.mediaType === 'IMAGE' ? (
                              <ImageIcon className="h-8 w-8 text-gray-400" />
                            ) : (
                              <Video className="h-8 w-8 text-gray-400" />
                            )}
                          </div>
                        )}
                        {item.type === 'review' && (
                          <div className="w-16 h-16 bg-yellow-50 rounded-lg flex items-center justify-center">
                            <Star className="h-8 w-8 text-yellow-500" />
                          </div>
                        )}
                      </div>

                      {/* Content Details */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-2">
                          <Badge className={getTypeColor(item.type)} size="sm">
                            {getTypeIcon(item.type)}
                            <span className="ml-1">{item.type.toUpperCase()}</span>
                          </Badge>
                          <Badge className={getStatusColor(item.status)} size="sm">
                            {item.status.toUpperCase()}
                          </Badge>
                        </div>

                        <h4 className="text-lg font-medium text-gray-900 mb-2">
                          {item.type === 'profile' && item.profileName}
                          {item.type === 'media' && item.mediaFilename}
                          {item.type === 'review' && `Review for ${item.reviewTargetProfile}`}
                        </h4>

                        <div className="text-sm text-gray-600 mb-3">
                          <p>Submitted by: {item.submittedBy} ({item.submittedByEmail})</p>
                          <p>Submitted: {formatDate(item.submittedAt)}</p>
                          {item.moderatedBy && (
                            <p>Moderated by: {item.moderatedBy} on {formatDate(item.moderatedAt!)}</p>
                          )}
                        </div>

                        {/* Content-specific details */}
                        {item.type === 'profile' && item.profileDescription && (
                          <div className="bg-gray-50 p-3 rounded mb-3">
                            <p className="text-sm">{item.profileDescription}</p>
                          </div>
                        )}

                        {item.type === 'review' && (
                          <div className="bg-gray-50 p-3 rounded mb-3">
                            <div className="flex items-center space-x-2 mb-2">
                              <span className="text-sm font-medium">Rating:</span>
                              <div className="flex">
                                {[1, 2, 3, 4, 5].map((star) => (
                                  <Star
                                    key={star}
                                    className={`h-4 w-4 ${
                                      star <= (item.reviewRating || 0)
                                        ? 'text-yellow-400 fill-current'
                                        : 'text-gray-300'
                                    }`}
                                  />
                                ))}
                              </div>
                            </div>
                            <p className="text-sm">{item.reviewComment}</p>
                          </div>
                        )}

                        {item.rejectionNote && (
                          <div className="bg-red-50 border border-red-200 p-3 rounded mb-3">
                            <p className="text-sm text-red-800">
                              <strong>Rejection Note:</strong> {item.rejectionNote}
                            </p>
                          </div>
                        )}

                        {/* Moderation Actions */}
                        {item.status === 'pending' && (
                          <div className="space-y-3">
                            <div>
                              <Textarea
                                placeholder="Rejection note (required if rejecting)..."
                                value={rejectionNote[item.id] || ''}
                                onChange={(e) => setRejectionNote(prev => ({
                                  ...prev,
                                  [item.id]: e.target.value
                                }))}
                                className="mb-2"
                              />
                            </div>
                            <div className="flex space-x-2">
                              <Button
                                onClick={() => handleApprove(item.id)}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <Check className="mr-2 h-4 w-4" />
                                Approve
                              </Button>
                              <Button
                                onClick={() => handleReject(item.id)}
                                variant="destructive"
                              >
                                <X className="mr-2 h-4 w-4" />
                                Reject
                              </Button>
                              <Button variant="outline">
                                <Eye className="mr-2 h-4 w-4" />
                                View Details
                              </Button>
                            </div>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

      </div>
    </AdminLayout>
  );
}

export default withAuth(AdminModerationPage, ['ADMIN']);
