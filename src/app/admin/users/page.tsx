'use client';

import { useState, useEffect } from 'react';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  Search, 
  Filter, 
  MoreHorizontal,
  Edit,
  Ban,
  CheckCircle,
  XCircle,
  Mail,
  Calendar,
  Eye
} from 'lucide-react';

interface AdminUser {
  id: string;
  username: string;
  email: string;
  role: string;
  status: string;
  emailVerified: boolean;
  createdAt: string;
  lastLogin?: string;
  profileName?: string;
  profileStatus?: string;
  totalBookings?: number;
  totalEarnings?: number;
}

function AdminUsersPage() {
  const { user } = useAuth();
  const [users, setUsers] = useState<AdminUser[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [statusFilter, setStatusFilter] = useState<string>('all');

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setUsers([
        {
          id: '1',
          username: 'admin',
          email: '<EMAIL>',
          role: 'ADMIN',
          status: 'ACTIVE',
          emailVerified: true,
          createdAt: '2023-01-15T10:00:00Z',
          lastLogin: '2024-01-20T14:30:00Z',
        },
        {
          id: '2',
          username: 'testmember',
          email: '<EMAIL>',
          role: 'MEMBER',
          status: 'ACTIVE',
          emailVerified: true,
          createdAt: '2023-06-20T15:30:00Z',
          lastLogin: '2024-01-19T18:45:00Z',
          totalBookings: 12,
        },
        {
          id: '3',
          username: 'testescort',
          email: '<EMAIL>',
          role: 'ESCORT',
          status: 'ACTIVE',
          emailVerified: true,
          createdAt: '2023-06-15T12:00:00Z',
          lastLogin: '2024-01-20T10:15:00Z',
          profileName: 'Sophia Elite',
          profileStatus: 'APPROVED',
          totalBookings: 45,
          totalEarnings: 13500,
        },
        {
          id: '4',
          username: 'testagency',
          email: '<EMAIL>',
          role: 'AGENCY',
          status: 'ACTIVE',
          emailVerified: true,
          createdAt: '2023-08-10T09:30:00Z',
          lastLogin: '2024-01-18T16:20:00Z',
          profileName: 'Elite Companions Agency',
          profileStatus: 'APPROVED',
          totalEarnings: 45600,
        },
        {
          id: '5',
          username: 'newuser',
          email: '<EMAIL>',
          role: 'ESCORT',
          status: 'PENDING_VERIFICATION',
          emailVerified: false,
          createdAt: '2024-01-18T14:20:00Z',
          profileName: 'Victoria Rose',
          profileStatus: 'PENDING_APPROVAL',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'ADMIN':
        return 'bg-red-100 text-red-800';
      case 'ESCORT':
        return 'bg-purple-100 text-purple-800';
      case 'AGENCY':
        return 'bg-blue-100 text-blue-800';
      case 'STUDIO':
        return 'bg-green-100 text-green-800';
      case 'MEMBER':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING_VERIFICATION':
        return 'bg-yellow-100 text-yellow-800';
      case 'SUSPENDED':
        return 'bg-red-100 text-red-800';
      case 'BANNED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatLastLogin = (dateString?: string) => {
    if (!dateString) return 'Never';
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         (user.profileName && user.profileName.toLowerCase().includes(searchTerm.toLowerCase()));
    
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    const matchesStatus = statusFilter === 'all' || user.status === statusFilter;
    
    return matchesSearch && matchesRole && matchesStatus;
  });

  const userStats = {
    total: users.length,
    active: users.filter(u => u.status === 'ACTIVE').length,
    pending: users.filter(u => u.status === 'PENDING_VERIFICATION').length,
    suspended: users.filter(u => u.status === 'SUSPENDED' || u.status === 'BANNED').length,
  };

  if (!user || user.role !== 'ADMIN') {
    return null;
  }

  return (
    <AdminLayout 
      title="User Management" 
      description="Manage platform users and their accounts"
    >
      <div className="px-4 py-6 sm:px-0">
        
        {/* User Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-blue-50">
                  <Users className="h-6 w-6 text-blue-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Users</p>
                  <p className="text-2xl font-bold text-gray-900">{userStats.total}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-green-50">
                  <CheckCircle className="h-6 w-6 text-green-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Active</p>
                  <p className="text-2xl font-bold text-gray-900">{userStats.active}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-yellow-50">
                  <Calendar className="h-6 w-6 text-yellow-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Pending</p>
                  <p className="text-2xl font-bold text-gray-900">{userStats.pending}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-red-50">
                  <XCircle className="h-6 w-6 text-red-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Suspended</p>
                  <p className="text-2xl font-bold text-gray-900">{userStats.suspended}</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5" />
              User Directory
            </CardTitle>
            <CardDescription>
              {filteredUsers.length} of {users.length} users
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row gap-4 mb-6">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search users by username, email, or profile name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              
              <div className="flex gap-2">
                <select
                  value={roleFilter}
                  onChange={(e) => setRoleFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="all">All Roles</option>
                  <option value="ADMIN">Admin</option>
                  <option value="ESCORT">Escort</option>
                  <option value="AGENCY">Agency</option>
                  <option value="STUDIO">Studio</option>
                  <option value="MEMBER">Member</option>
                </select>
                
                <select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="all">All Status</option>
                  <option value="ACTIVE">Active</option>
                  <option value="PENDING_VERIFICATION">Pending</option>
                  <option value="SUSPENDED">Suspended</option>
                  <option value="BANNED">Banned</option>
                </select>
              </div>
            </div>

            {/* Users List */}
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3, 4, 5].map((i) => (
                  <div key={i} className="animate-pulse border rounded-lg p-4">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredUsers.length === 0 ? (
              <div className="text-center py-8">
                <Users className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No users found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Try adjusting your search or filter criteria.
                </p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredUsers.map((user) => (
                  <div key={user.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <Avatar className="h-12 w-12">
                        <AvatarFallback>{user.username.charAt(0).toUpperCase()}</AvatarFallback>
                      </Avatar>
                      
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center space-x-2 mb-1">
                          <h4 className="text-sm font-medium text-gray-900 truncate">
                            {user.profileName || user.username}
                          </h4>
                          <Badge className={getRoleColor(user.role)} size="sm">
                            {user.role}
                          </Badge>
                          <Badge className={getStatusColor(user.status)} size="sm">
                            {user.status.replace('_', ' ')}
                          </Badge>
                          {!user.emailVerified && (
                            <Badge variant="outline" size="sm" className="text-red-600">
                              Unverified
                            </Badge>
                          )}
                        </div>
                        
                        <div className="grid grid-cols-1 md:grid-cols-3 gap-2 text-sm text-gray-600">
                          <div className="flex items-center">
                            <Mail className="mr-1 h-3 w-3" />
                            {user.email}
                          </div>
                          <div className="flex items-center">
                            <Calendar className="mr-1 h-3 w-3" />
                            Joined {formatDate(user.createdAt)}
                          </div>
                          <div className="flex items-center">
                            <Eye className="mr-1 h-3 w-3" />
                            Last login {formatLastLogin(user.lastLogin)}
                          </div>
                        </div>
                        
                        {(user.totalBookings || user.totalEarnings) && (
                          <div className="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                            {user.totalBookings && (
                              <span>{user.totalBookings} bookings</span>
                            )}
                            {user.totalEarnings && (
                              <span>€{user.totalEarnings.toLocaleString()} earned</span>
                            )}
                          </div>
                        )}
                      </div>

                      {/* Action Buttons */}
                      <div className="flex items-center space-x-2">
                        <Button variant="ghost" size="sm">
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        {user.status === 'ACTIVE' ? (
                          <Button variant="ghost" size="sm" className="text-red-600">
                            <Ban className="h-4 w-4" />
                          </Button>
                        ) : (
                          <Button variant="ghost" size="sm" className="text-green-600">
                            <CheckCircle className="h-4 w-4" />
                          </Button>
                        )}
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

      </div>
    </AdminLayout>
  );
}

export default withAuth(AdminUsersPage, ['ADMIN']);
