'use client';

import { useState, useEffect } from 'react';
import { useAuth, withAuth } from '@/contexts/AuthContext';
import AdminLayout from '@/components/admin/AdminLayout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { 
  FileText, 
  Plus, 
  Edit, 
  Trash2, 
  Eye,
  Save,
  X,
  Globe,
  Lock
} from 'lucide-react';

interface ContentItem {
  id: string;
  title: string;
  slug: string;
  content: string;
  type: 'PAGE' | 'POST' | 'POLICY' | 'FAQ';
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  author: string;
}

function AdminContentPage() {
  const { user } = useAuth();
  const [content, setContent] = useState<ContentItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [editingItem, setEditingItem] = useState<ContentItem | null>(null);
  const [isCreating, setIsCreating] = useState(false);
  const [filter, setFilter] = useState<'all' | 'PAGE' | 'POST' | 'POLICY' | 'FAQ'>('all');

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setContent([
        {
          id: '1',
          title: 'Privacy Policy',
          slug: 'privacy-policy',
          content: 'Our comprehensive privacy policy explaining how we handle user data...',
          type: 'POLICY',
          status: 'PUBLISHED',
          isPublic: true,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-20T14:30:00Z',
          author: 'admin',
        },
        {
          id: '2',
          title: 'Terms of Service',
          slug: 'terms-of-service',
          content: 'Terms and conditions for using our platform...',
          type: 'POLICY',
          status: 'PUBLISHED',
          isPublic: true,
          createdAt: '2024-01-15T10:00:00Z',
          updatedAt: '2024-01-18T16:20:00Z',
          author: 'admin',
        },
        {
          id: '3',
          title: 'How to Create a Profile',
          slug: 'how-to-create-profile',
          content: 'Step-by-step guide for creating an attractive profile...',
          type: 'FAQ',
          status: 'PUBLISHED',
          isPublic: true,
          createdAt: '2024-01-16T12:00:00Z',
          updatedAt: '2024-01-19T09:15:00Z',
          author: 'admin',
        },
        {
          id: '4',
          title: 'Platform Updates January 2024',
          slug: 'platform-updates-jan-2024',
          content: 'Latest updates and improvements to the platform...',
          type: 'POST',
          status: 'DRAFT',
          isPublic: false,
          createdAt: '2024-01-20T08:00:00Z',
          updatedAt: '2024-01-20T08:00:00Z',
          author: 'admin',
        },
        {
          id: '5',
          title: 'About Us',
          slug: 'about',
          content: 'Learn more about our company and mission...',
          type: 'PAGE',
          status: 'PUBLISHED',
          isPublic: true,
          createdAt: '2024-01-10T15:30:00Z',
          updatedAt: '2024-01-15T11:45:00Z',
          author: 'admin',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'PAGE':
        return 'bg-blue-100 text-blue-800';
      case 'POST':
        return 'bg-green-100 text-green-800';
      case 'POLICY':
        return 'bg-red-100 text-red-800';
      case 'FAQ':
        return 'bg-yellow-100 text-yellow-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'PUBLISHED':
        return 'bg-green-100 text-green-800';
      case 'DRAFT':
        return 'bg-yellow-100 text-yellow-800';
      case 'ARCHIVED':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleSave = (item: ContentItem) => {
    if (isCreating) {
      // Add new item
      const newItem = {
        ...item,
        id: Date.now().toString(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        author: user?.username || 'admin',
      };
      setContent([newItem, ...content]);
      setIsCreating(false);
    } else {
      // Update existing item
      setContent(content.map(c => 
        c.id === item.id 
          ? { ...item, updatedAt: new Date().toISOString() }
          : c
      ));
    }
    setEditingItem(null);
  };

  const handleDelete = (id: string) => {
    if (confirm('Are you sure you want to delete this content?')) {
      setContent(content.filter(c => c.id !== id));
    }
  };

  const handleCreate = () => {
    const newItem: ContentItem = {
      id: 'new',
      title: '',
      slug: '',
      content: '',
      type: 'PAGE',
      status: 'DRAFT',
      isPublic: false,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      author: user?.username || 'admin',
    };
    setEditingItem(newItem);
    setIsCreating(true);
  };

  const filteredContent = content.filter(item => 
    filter === 'all' || item.type === filter
  );

  if (!user || user.role !== 'ADMIN') {
    return null;
  }

  return (
    <AdminLayout 
      title="Content Management" 
      description="Manage website content, policies, and documentation"
    >
      <div className="px-4 py-6 sm:px-0">
        
        {/* Content Statistics */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-blue-50">
                  <FileText className="h-6 w-6 text-blue-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Content</p>
                  <p className="text-2xl font-bold text-gray-900">{content.length}</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-green-50">
                  <Globe className="h-6 w-6 text-green-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Published</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {content.filter(c => c.status === 'PUBLISHED').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-yellow-50">
                  <Edit className="h-6 w-6 text-yellow-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Drafts</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {content.filter(c => c.status === 'DRAFT').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="p-2 rounded-lg bg-red-50">
                  <Lock className="h-6 w-6 text-red-500" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Policies</p>
                  <p className="text-2xl font-bold text-gray-900">
                    {content.filter(c => c.type === 'POLICY').length}
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Content Management */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="flex items-center">
                  <FileText className="mr-2 h-5 w-5" />
                  Content Library
                </CardTitle>
                <CardDescription>
                  {filteredContent.length} content items
                </CardDescription>
              </div>
              <div className="flex space-x-2">
                <select
                  value={filter}
                  onChange={(e) => setFilter(e.target.value as any)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="all">All Types</option>
                  <option value="PAGE">Pages</option>
                  <option value="POST">Posts</option>
                  <option value="POLICY">Policies</option>
                  <option value="FAQ">FAQ</option>
                </select>
                <Button onClick={handleCreate}>
                  <Plus className="mr-2 h-4 w-4" />
                  Create Content
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            
            {/* Edit Form */}
            {editingItem && (
              <div className="mb-6 p-6 border rounded-lg bg-gray-50">
                <h3 className="text-lg font-medium mb-4">
                  {isCreating ? 'Create New Content' : 'Edit Content'}
                </h3>
                
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Title
                    </label>
                    <Input
                      value={editingItem.title}
                      onChange={(e) => setEditingItem({
                        ...editingItem,
                        title: e.target.value,
                        slug: e.target.value.toLowerCase().replace(/[^a-z0-9]+/g, '-').replace(/(^-|-$)/g, '')
                      })}
                      placeholder="Content title"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Slug
                    </label>
                    <Input
                      value={editingItem.slug}
                      onChange={(e) => setEditingItem({
                        ...editingItem,
                        slug: e.target.value
                      })}
                      placeholder="url-slug"
                    />
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Type
                    </label>
                    <select
                      value={editingItem.type}
                      onChange={(e) => setEditingItem({
                        ...editingItem,
                        type: e.target.value as any
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="PAGE">Page</option>
                      <option value="POST">Post</option>
                      <option value="POLICY">Policy</option>
                      <option value="FAQ">FAQ</option>
                    </select>
                  </div>
                  
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Status
                    </label>
                    <select
                      value={editingItem.status}
                      onChange={(e) => setEditingItem({
                        ...editingItem,
                        status: e.target.value as any
                      })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md"
                    >
                      <option value="DRAFT">Draft</option>
                      <option value="PUBLISHED">Published</option>
                      <option value="ARCHIVED">Archived</option>
                    </select>
                  </div>
                </div>
                
                <div className="mb-4">
                  <div className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="isPublic"
                      checked={editingItem.isPublic}
                      onChange={(e) => setEditingItem({
                        ...editingItem,
                        isPublic: e.target.checked
                      })}
                      className="rounded"
                    />
                    <label htmlFor="isPublic" className="text-sm font-medium text-gray-700">
                      Public (visible to non-logged-in users)
                    </label>
                  </div>
                </div>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Content
                  </label>
                  <Textarea
                    value={editingItem.content}
                    onChange={(e) => setEditingItem({
                      ...editingItem,
                      content: e.target.value
                    })}
                    placeholder="Content body..."
                    rows={10}
                  />
                </div>
                
                <div className="flex space-x-2">
                  <Button onClick={() => handleSave(editingItem)}>
                    <Save className="mr-2 h-4 w-4" />
                    Save
                  </Button>
                  <Button 
                    variant="outline" 
                    onClick={() => {
                      setEditingItem(null);
                      setIsCreating(false);
                    }}
                  >
                    <X className="mr-2 h-4 w-4" />
                    Cancel
                  </Button>
                </div>
              </div>
            )}

            {/* Content List */}
            {isLoading ? (
              <div className="space-y-4">
                {[1, 2, 3].map((i) => (
                  <div key={i} className="animate-pulse border rounded-lg p-4">
                    <div className="flex items-center space-x-4">
                      <div className="flex-1 space-y-2">
                        <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                        <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : filteredContent.length === 0 ? (
              <div className="text-center py-8">
                <FileText className="mx-auto h-12 w-12 text-gray-400" />
                <h3 className="mt-2 text-sm font-medium text-gray-900">No content found</h3>
                <p className="mt-1 text-sm text-gray-500">
                  Create your first content item to get started.
                </p>
                <div className="mt-6">
                  <Button onClick={handleCreate}>
                    <Plus className="mr-2 h-4 w-4" />
                    Create Content
                  </Button>
                </div>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredContent.map((item) => (
                  <div key={item.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center space-x-2 mb-2">
                          <h4 className="text-lg font-medium text-gray-900">
                            {item.title}
                          </h4>
                          <Badge className={getTypeColor(item.type)} size="sm">
                            {item.type}
                          </Badge>
                          <Badge className={getStatusColor(item.status)} size="sm">
                            {item.status}
                          </Badge>
                          {item.isPublic && (
                            <Badge variant="outline" size="sm">
                              Public
                            </Badge>
                          )}
                        </div>
                        
                        <p className="text-sm text-gray-600 mb-2">
                          Slug: /{item.slug}
                        </p>
                        
                        <p className="text-sm text-gray-700 mb-3 line-clamp-2">
                          {item.content}
                        </p>
                        
                        <div className="text-xs text-gray-500">
                          Created: {formatDate(item.createdAt)} • 
                          Updated: {formatDate(item.updatedAt)} • 
                          Author: {item.author}
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-2 ml-4">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => setEditingItem(item)}
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button variant="ghost" size="sm">
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleDelete(item.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

      </div>
    </AdminLayout>
  );
}

export default withAuth(AdminContentPage, ['ADMIN']);
