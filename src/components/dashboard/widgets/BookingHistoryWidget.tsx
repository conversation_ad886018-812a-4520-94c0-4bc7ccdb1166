'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Calendar, Clock, MapPin, Star } from 'lucide-react';
import Link from 'next/link';

interface Booking {
  id: string;
  profileName: string;
  profileAlias?: string;
  serviceName?: string;
  startTime: string;
  endTime: string;
  totalPrice: number;
  status: 'PENDING' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED';
  canReview: boolean;
  hasReview: boolean;
}

export default function BookingHistoryWidget() {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setBookings([
        {
          id: '1',
          profileName: 'Sophia Elite',
          profileAlias: 'Sophia',
          serviceName: 'Dinner Date',
          startTime: '2024-01-15T19:00:00Z',
          endTime: '2024-01-15T22:00:00Z',
          totalPrice: 400,
          status: 'COMPLETED',
          canReview: true,
          hasReview: false,
        },
        {
          id: '2',
          profileName: 'Isabella Grace',
          serviceName: 'Overnight Companion',
          startTime: '2024-01-10T20:00:00Z',
          endTime: '2024-01-11T08:00:00Z',
          totalPrice: 1200,
          status: 'COMPLETED',
          canReview: true,
          hasReview: true,
        },
        {
          id: '3',
          profileName: 'Victoria Elite',
          serviceName: 'Social Event',
          startTime: '2024-01-25T18:00:00Z',
          endTime: '2024-01-25T23:00:00Z',
          totalPrice: 600,
          status: 'CONFIRMED',
          canReview: false,
          hasReview: false,
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'CONFIRMED':
        return 'bg-blue-100 text-blue-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Recent Bookings
          </CardTitle>
          <CardDescription>Your booking history</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Calendar className="mr-2 h-5 w-5" />
          Recent Bookings
        </CardTitle>
        <CardDescription>
          {bookings.length} booking{bookings.length !== 1 ? 's' : ''} in your history
        </CardDescription>
      </CardHeader>
      <CardContent>
        {bookings.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No bookings yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start browsing profiles to make your first booking.
            </p>
            <div className="mt-6">
              <Link href="/browse">
                <Button>Browse Profiles</Button>
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {bookings.slice(0, 3).map((booking) => (
              <div key={booking.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="text-sm font-medium text-gray-900">
                        {booking.profileAlias || booking.profileName}
                      </h4>
                      <Badge className={getStatusColor(booking.status)} size="sm">
                        {booking.status}
                      </Badge>
                    </div>
                    
                    {booking.serviceName && (
                      <p className="text-sm text-gray-600 mb-2">{booking.serviceName}</p>
                    )}
                    
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center">
                        <Calendar className="mr-1 h-3 w-3" />
                        {formatDate(booking.startTime)}
                      </div>
                      <div className="flex items-center">
                        <Clock className="mr-1 h-3 w-3" />
                        {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right">
                    <p className="text-sm font-medium text-gray-900">
                      €{booking.totalPrice}
                    </p>
                    {booking.canReview && !booking.hasReview && booking.status === 'COMPLETED' && (
                      <Button variant="outline" size="sm" className="mt-2">
                        <Star className="mr-1 h-3 w-3" />
                        Review
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
            
            <div className="pt-4 border-t">
              <Link href="/dashboard/bookings">
                <Button variant="outline" className="w-full">
                  View All Bookings
                </Button>
              </Link>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
