'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Heart, MapPin, Star, Eye } from 'lucide-react';
import Link from 'next/link';

interface FavoriteProfile {
  id: string;
  name: string;
  alias?: string;
  location: string;
  profileImage?: string;
  hourlyRate?: number;
  averageRating: number;
  isAvailable: boolean;
  profileType: string;
}

export default function FavoritesWidget() {
  const [favorites, setFavorites] = useState<FavoriteProfile[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setFavorites([
        {
          id: '1',
          name: 'Sophia Elite',
          alias: 'Sophia',
          location: 'Berlin, Germany',
          profileImage: '/placeholder-avatar.jpg',
          hourlyRate: 300,
          averageRating: 4.8,
          isAvailable: true,
          profileType: 'ESCORT',
        },
        {
          id: '2',
          name: 'Elite Companions Agency',
          location: 'Munich, Germany',
          profileImage: '/placeholder-agency.jpg',
          averageRating: 4.6,
          isAvailable: true,
          profileType: 'AGENCY',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const removeFavorite = (profileId: string) => {
    setFavorites(favorites.filter(fav => fav.id !== profileId));
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Heart className="mr-2 h-5 w-5" />
            My Favorites
          </CardTitle>
          <CardDescription>Your saved profiles</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Heart className="mr-2 h-5 w-5" />
          My Favorites
        </CardTitle>
        <CardDescription>
          {favorites.length} saved profile{favorites.length !== 1 ? 's' : ''}
        </CardDescription>
      </CardHeader>
      <CardContent>
        {favorites.length === 0 ? (
          <div className="text-center py-8">
            <Heart className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No favorites yet</h3>
            <p className="mt-1 text-sm text-gray-500">
              Start browsing profiles to add them to your favorites.
            </p>
            <div className="mt-6">
              <Link href="/browse">
                <Button>Browse Profiles</Button>
              </Link>
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            {favorites.map((profile) => (
              <div key={profile.id} className="flex items-center space-x-4 p-3 border rounded-lg hover:bg-gray-50">
                <div className="w-12 h-12 bg-gray-200 rounded-full flex items-center justify-center">
                  {profile.profileImage ? (
                    <img
                      src={profile.profileImage}
                      alt={profile.name}
                      className="w-12 h-12 rounded-full object-cover"
                    />
                  ) : (
                    <span className="text-lg font-medium text-gray-600">
                      {profile.name.charAt(0)}
                    </span>
                  )}
                </div>
                
                <div className="flex-1 min-w-0">
                  <div className="flex items-center space-x-2">
                    <p className="text-sm font-medium text-gray-900 truncate">
                      {profile.alias || profile.name}
                    </p>
                    <Badge variant={profile.isAvailable ? 'default' : 'secondary'} size="sm">
                      {profile.isAvailable ? 'Available' : 'Unavailable'}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center space-x-4 mt-1">
                    <div className="flex items-center text-xs text-gray-500">
                      <MapPin className="mr-1 h-3 w-3" />
                      {profile.location}
                    </div>
                    
                    <div className="flex items-center text-xs text-gray-500">
                      <Star className="mr-1 h-3 w-3 fill-current text-yellow-400" />
                      {profile.averageRating.toFixed(1)}
                    </div>
                    
                    {profile.hourlyRate && (
                      <div className="text-xs font-medium text-gray-900">
                        €{profile.hourlyRate}/hr
                      </div>
                    )}
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Link href={`/profile/${profile.id}`}>
                    <Button variant="ghost" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </Link>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeFavorite(profile.id)}
                    className="text-red-600 hover:text-red-700"
                  >
                    <Heart className="h-4 w-4 fill-current" />
                  </Button>
                </div>
              </div>
            ))}
            
            <div className="pt-4 border-t">
              <Link href="/dashboard/favorites">
                <Button variant="outline" className="w-full">
                  View All Favorites
                </Button>
              </Link>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
