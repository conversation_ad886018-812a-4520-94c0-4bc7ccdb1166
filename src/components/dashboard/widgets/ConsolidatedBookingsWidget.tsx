'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Calendar, 
  Clock, 
  DollarSign, 
  User, 
  Filter,
  TrendingUp,
  CheckCircle,
  AlertTriangle,
  XCircle
} from 'lucide-react';

interface ConsolidatedBooking {
  id: string;
  escortName: string;
  escortAlias: string;
  escortImage?: string;
  clientName: string;
  serviceName: string;
  startTime: string;
  endTime: string;
  totalPrice: number;
  status: 'PENDING' | 'CONFIRMED' | 'COMPLETED' | 'CANCELLED';
  commission: number;
  createdAt: string;
}

export default function ConsolidatedBookingsWidget() {
  const [bookings, setBookings] = useState<ConsolidatedBooking[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'today' | 'pending' | 'confirmed'>('all');
  const [timeRange, setTimeRange] = useState<'today' | 'week' | 'month'>('today');

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setBookings([
        {
          id: '1',
          escortName: 'Sophia Elite',
          escortAlias: 'Sophia',
          escortImage: '/placeholder-avatar.jpg',
          clientName: 'John D.',
          serviceName: 'Dinner Date',
          startTime: '2024-01-25T19:00:00Z',
          endTime: '2024-01-25T22:00:00Z',
          totalPrice: 400,
          status: 'PENDING',
          commission: 80, // 20% commission
          createdAt: '2024-01-20T10:30:00Z',
        },
        {
          id: '2',
          escortName: 'Isabella Grace',
          escortAlias: 'Isabella',
          escortImage: '/placeholder-avatar-2.jpg',
          clientName: 'Michael S.',
          serviceName: 'Social Event',
          startTime: '2024-01-24T18:00:00Z',
          endTime: '2024-01-24T23:00:00Z',
          totalPrice: 600,
          status: 'CONFIRMED',
          commission: 120,
          createdAt: '2024-01-18T14:15:00Z',
        },
        {
          id: '3',
          escortName: 'Sophia Elite',
          escortAlias: 'Sophia',
          escortImage: '/placeholder-avatar.jpg',
          clientName: 'Robert K.',
          serviceName: 'Overnight Companion',
          startTime: '2024-01-23T20:00:00Z',
          endTime: '2024-01-24T08:00:00Z',
          totalPrice: 1200,
          status: 'COMPLETED',
          commission: 240,
          createdAt: '2024-01-19T16:45:00Z',
        },
        {
          id: '4',
          escortName: 'Isabella Grace',
          escortAlias: 'Isabella',
          clientName: 'David L.',
          serviceName: 'Business Dinner',
          startTime: '2024-01-22T19:30:00Z',
          endTime: '2024-01-22T22:30:00Z',
          totalPrice: 450,
          status: 'CANCELLED',
          commission: 0,
          createdAt: '2024-01-17T11:20:00Z',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'PENDING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-blue-500" />;
      case 'CANCELLED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    }
    
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const filteredBookings = bookings.filter(booking => {
    const bookingDate = new Date(booking.startTime);
    const today = new Date();
    
    switch (filter) {
      case 'today':
        return bookingDate.toDateString() === today.toDateString();
      case 'pending':
        return booking.status === 'PENDING';
      case 'confirmed':
        return booking.status === 'CONFIRMED';
      default:
        return true;
    }
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Consolidated Bookings
          </CardTitle>
          <CardDescription>All bookings across your escorts</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse border rounded-lg p-4">
                <div className="flex items-center space-x-4">
                  <div className="w-10 h-10 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const pendingBookings = bookings.filter(b => b.status === 'PENDING');
  const confirmedBookings = bookings.filter(b => b.status === 'CONFIRMED');
  const completedBookings = bookings.filter(b => b.status === 'COMPLETED');
  const totalCommission = bookings.reduce((sum, b) => sum + b.commission, 0);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              Consolidated Bookings
              {pendingBookings.length > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {pendingBookings.length} pending
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {bookings.length} total bookings • €{totalCommission} total commission
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              Filter
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Filter Tabs */}
        <div className="flex space-x-2">
          {(['all', 'today', 'pending', 'confirmed'] as const).map((filterType) => (
            <Button
              key={filterType}
              variant={filter === filterType ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter(filterType)}
            >
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
              {filterType === 'pending' && pendingBookings.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {pendingBookings.length}
                </Badge>
              )}
            </Button>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{pendingBookings.length}</div>
            <div className="text-sm text-yellow-700">Pending</div>
          </div>
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{confirmedBookings.length}</div>
            <div className="text-sm text-green-700">Confirmed</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{completedBookings.length}</div>
            <div className="text-sm text-blue-700">Completed</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">€{totalCommission}</div>
            <div className="text-sm text-purple-700">Commission</div>
          </div>
        </div>

        {/* Bookings List */}
        {filteredBookings.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No {filter !== 'all' ? filter : ''} bookings
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter === 'today' 
                ? 'No bookings scheduled for today.' 
                : 'Bookings will appear here as they come in.'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredBookings.map((booking) => (
              <div key={booking.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-start space-x-4">
                  <Avatar className="h-10 w-10">
                    <AvatarImage src={booking.escortImage} alt={booking.escortName} />
                    <AvatarFallback>{booking.escortAlias.charAt(0)}</AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="text-sm font-medium text-gray-900">
                        {booking.serviceName}
                      </h4>
                      <Badge className={getStatusColor(booking.status)} size="sm">
                        {getStatusIcon(booking.status)}
                        <span className="ml-1">{booking.status}</span>
                      </Badge>
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mb-2">
                      <div className="flex items-center">
                        <User className="mr-2 h-3 w-3" />
                        {booking.escortAlias} → {booking.clientName}
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="mr-2 h-3 w-3" />
                        €{booking.totalPrice} (€{booking.commission} commission)
                      </div>
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-3 w-3" />
                        {formatDate(booking.startTime)}
                      </div>
                      <div className="flex items-center">
                        <Clock className="mr-2 h-3 w-3" />
                        {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
                      </div>
                    </div>
                  </div>

                  {/* Revenue Info */}
                  <div className="text-right">
                    <div className="text-sm font-medium text-gray-900">
                      €{booking.totalPrice}
                    </div>
                    <div className="text-xs text-gray-500">
                      €{booking.commission} commission
                    </div>
                    {booking.status === 'COMPLETED' && (
                      <Badge variant="outline" size="sm" className="mt-1 text-green-600">
                        Paid
                      </Badge>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Summary Stats */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-center">
            <div>
              <div className="text-lg font-bold text-gray-900">
                €{bookings.reduce((sum, b) => sum + b.totalPrice, 0).toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">Total Revenue</div>
            </div>
            <div>
              <div className="text-lg font-bold text-gray-900">
                €{totalCommission.toLocaleString()}
              </div>
              <div className="text-xs text-gray-500">Total Commission</div>
            </div>
            <div>
              <div className="text-lg font-bold text-gray-900">
                €{(bookings.reduce((sum, b) => sum + b.totalPrice, 0) / bookings.length || 0).toFixed(0)}
              </div>
              <div className="text-xs text-gray-500">Avg Booking Value</div>
            </div>
            <div>
              <div className="text-lg font-bold text-gray-900">
                {((totalCommission / bookings.reduce((sum, b) => sum + b.totalPrice, 0)) * 100 || 0).toFixed(1)}%
              </div>
              <div className="text-xs text-gray-500">Commission Rate</div>
            </div>
          </div>
        </div>

      </CardContent>
    </Card>
  );
}
