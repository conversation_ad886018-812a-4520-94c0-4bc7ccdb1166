'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { 
  Calendar, 
  Clock, 
  Plus, 
  Edit, 
  Trash2,
  CheckCircle,
  XCircle,
  AlertCircle
} from 'lucide-react';

interface AvailabilitySlot {
  id: string;
  date: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  isBooked: boolean;
  bookingId?: string;
  notes?: string;
}

export default function AvailabilityWidget() {
  const [availability, setAvailability] = useState<AvailabilitySlot[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isGloballyAvailable, setIsGloballyAvailable] = useState(true);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      const today = new Date();
      const slots: AvailabilitySlot[] = [];
      
      // Generate availability for next 7 days
      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        const dateString = date.toISOString().split('T')[0];
        
        // Morning slot
        slots.push({
          id: `${dateString}-morning`,
          date: dateString,
          startTime: '10:00',
          endTime: '14:00',
          isAvailable: i % 3 !== 0, // Some days unavailable
          isBooked: i === 1, // Tomorrow is booked
          bookingId: i === 1 ? 'booking-123' : undefined,
        });
        
        // Evening slot
        slots.push({
          id: `${dateString}-evening`,
          date: dateString,
          startTime: '18:00',
          endTime: '23:00',
          isAvailable: i % 2 === 0,
          isBooked: i === 2, // Day after tomorrow evening is booked
          bookingId: i === 2 ? 'booking-456' : undefined,
        });
      }
      
      setAvailability(slots);
      setIsLoading(false);
    }, 1000);
  }, []);

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    const today = new Date();
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);
    
    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-US', {
        weekday: 'short',
        month: 'short',
        day: 'numeric',
      });
    }
  };

  const getSlotStatus = (slot: AvailabilitySlot) => {
    if (slot.isBooked) return 'booked';
    if (slot.isAvailable) return 'available';
    return 'unavailable';
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'available':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'booked':
        return <AlertCircle className="h-4 w-4 text-blue-500" />;
      case 'unavailable':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <XCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'available':
        return 'bg-green-100 text-green-800';
      case 'booked':
        return 'bg-blue-100 text-blue-800';
      case 'unavailable':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const toggleSlotAvailability = (slotId: string) => {
    setAvailability(availability.map(slot => 
      slot.id === slotId && !slot.isBooked
        ? { ...slot, isAvailable: !slot.isAvailable }
        : slot
    ));
  };

  const deleteSlot = (slotId: string) => {
    setAvailability(availability.filter(slot => slot.id !== slotId));
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Availability Calendar
          </CardTitle>
          <CardDescription>Manage your availability</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="flex items-center space-x-4">
                  <div className="w-16 h-8 bg-gray-200 rounded"></div>
                  <div className="flex-1 h-8 bg-gray-200 rounded"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const todaySlots = availability.filter(slot => slot.date === selectedDate);
  const availableSlots = availability.filter(slot => slot.isAvailable && !slot.isBooked);
  const bookedSlots = availability.filter(slot => slot.isBooked);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              Availability Calendar
            </CardTitle>
            <CardDescription>
              {availableSlots.length} available slots • {bookedSlots.length} booked
            </CardDescription>
          </div>
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="global-availability"
                checked={isGloballyAvailable}
                onCheckedChange={setIsGloballyAvailable}
              />
              <Label htmlFor="global-availability" className="text-sm">
                Available for bookings
              </Label>
            </div>
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Slot
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Global Status */}
        <div className={`p-3 rounded-lg ${
          isGloballyAvailable ? 'bg-green-50 border border-green-200' : 'bg-red-50 border border-red-200'
        }`}>
          <div className="flex items-center space-x-2">
            {isGloballyAvailable ? (
              <CheckCircle className="h-5 w-5 text-green-600" />
            ) : (
              <XCircle className="h-5 w-5 text-red-600" />
            )}
            <span className={`font-medium ${
              isGloballyAvailable ? 'text-green-800' : 'text-red-800'
            }`}>
              {isGloballyAvailable ? 'Currently accepting bookings' : 'Not accepting new bookings'}
            </span>
          </div>
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{availableSlots.length}</div>
            <div className="text-sm text-green-700">Available</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">{bookedSlots.length}</div>
            <div className="text-sm text-blue-700">Booked</div>
          </div>
          <div className="text-center p-3 bg-gray-50 rounded-lg">
            <div className="text-2xl font-bold text-gray-600">
              {availability.filter(slot => !slot.isAvailable && !slot.isBooked).length}
            </div>
            <div className="text-sm text-gray-700">Blocked</div>
          </div>
        </div>

        {/* Date Selector */}
        <div className="flex space-x-2 overflow-x-auto pb-2">
          {Array.from(new Set(availability.map(slot => slot.date))).map((date) => (
            <Button
              key={date}
              variant={selectedDate === date ? 'default' : 'outline'}
              size="sm"
              onClick={() => setSelectedDate(date)}
              className="whitespace-nowrap"
            >
              {formatDate(date)}
            </Button>
          ))}
        </div>

        {/* Availability Slots */}
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">
            {formatDate(selectedDate)} - {todaySlots.length} slots
          </h4>
          
          {todaySlots.length === 0 ? (
            <div className="text-center py-6 text-gray-500">
              <Calendar className="mx-auto h-8 w-8 text-gray-400 mb-2" />
              <p className="text-sm">No availability slots for this date</p>
              <Button variant="outline" size="sm" className="mt-2">
                <Plus className="mr-2 h-4 w-4" />
                Add Availability
              </Button>
            </div>
          ) : (
            <div className="space-y-2">
              {todaySlots.map((slot) => {
                const status = getSlotStatus(slot);
                return (
                  <div
                    key={slot.id}
                    className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50"
                  >
                    <div className="flex items-center space-x-3">
                      {getStatusIcon(status)}
                      <div>
                        <div className="flex items-center space-x-2">
                          <span className="font-medium text-gray-900">
                            {slot.startTime} - {slot.endTime}
                          </span>
                          <Badge className={getStatusColor(status)} size="sm">
                            {status.charAt(0).toUpperCase() + status.slice(1)}
                          </Badge>
                        </div>
                        {slot.isBooked && slot.bookingId && (
                          <p className="text-xs text-gray-500">
                            Booking ID: {slot.bookingId}
                          </p>
                        )}
                        {slot.notes && (
                          <p className="text-xs text-gray-600">{slot.notes}</p>
                        )}
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-2">
                      {!slot.isBooked && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => toggleSlotAvailability(slot.id)}
                        >
                          {slot.isAvailable ? 'Block' : 'Unblock'}
                        </Button>
                      )}
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                      {!slot.isBooked && (
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => deleteSlot(slot.id)}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2 pt-4 border-t">
          <Button variant="outline" size="sm">
            <Calendar className="mr-2 h-4 w-4" />
            Calendar View
          </Button>
          <Button variant="outline" size="sm">
            <Clock className="mr-2 h-4 w-4" />
            Set Regular Hours
          </Button>
          <Button variant="outline" size="sm">
            Export Schedule
          </Button>
        </div>

      </CardContent>
    </Card>
  );
}
