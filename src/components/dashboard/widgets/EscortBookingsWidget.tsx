'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Calendar, 
  Clock, 
  User, 
  Phone, 
  Mail, 
  MapPin,
  DollarSign,
  Check,
  X,
  MessageSquare,
  AlertTriangle
} from 'lucide-react';

interface BookingRequest {
  id: string;
  clientName: string;
  clientEmail: string;
  clientPhone?: string;
  serviceName: string;
  startTime: string;
  endTime: string;
  totalPrice: number;
  status: 'PENDING' | 'CONFIRMED' | 'CANCELLED' | 'COMPLETED';
  notes?: string;
  createdAt: string;
  isUrgent?: boolean;
}

export default function EscortBookingsWidget() {
  const [bookings, setBookings] = useState<BookingRequest[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'pending' | 'confirmed' | 'today'>('pending');

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setBookings([
        {
          id: '1',
          clientName: 'John D.',
          clientEmail: '<EMAIL>',
          clientPhone: '+49 ************',
          serviceName: 'Dinner Date',
          startTime: '2024-01-25T19:00:00Z',
          endTime: '2024-01-25T22:00:00Z',
          totalPrice: 400,
          status: 'PENDING',
          notes: 'Looking for an elegant companion for a business dinner.',
          createdAt: '2024-01-20T10:30:00Z',
          isUrgent: true,
        },
        {
          id: '2',
          clientName: 'Michael S.',
          clientEmail: '<EMAIL>',
          serviceName: 'Social Event',
          startTime: '2024-01-28T18:00:00Z',
          endTime: '2024-01-28T23:00:00Z',
          totalPrice: 600,
          status: 'CONFIRMED',
          notes: 'Company gala event, formal attire required.',
          createdAt: '2024-01-18T14:15:00Z',
        },
        {
          id: '3',
          clientName: 'Robert K.',
          clientEmail: '<EMAIL>',
          serviceName: 'Overnight Companion',
          startTime: '2024-01-30T20:00:00Z',
          endTime: '2024-01-31T08:00:00Z',
          totalPrice: 1200,
          status: 'PENDING',
          createdAt: '2024-01-19T16:45:00Z',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'CONFIRMED':
        return 'bg-green-100 text-green-800';
      case 'PENDING':
        return 'bg-yellow-100 text-yellow-800';
      case 'CANCELLED':
        return 'bg-red-100 text-red-800';
      case 'COMPLETED':
        return 'bg-blue-100 text-blue-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', {
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const handleAcceptBooking = (bookingId: string) => {
    setBookings(bookings.map(booking => 
      booking.id === bookingId 
        ? { ...booking, status: 'CONFIRMED' as const }
        : booking
    ));
  };

  const handleDeclineBooking = (bookingId: string) => {
    setBookings(bookings.map(booking => 
      booking.id === bookingId 
        ? { ...booking, status: 'CANCELLED' as const }
        : booking
    ));
  };

  const filteredBookings = bookings.filter(booking => {
    switch (filter) {
      case 'pending':
        return booking.status === 'PENDING';
      case 'confirmed':
        return booking.status === 'CONFIRMED';
      case 'today':
        const today = new Date().toDateString();
        return new Date(booking.startTime).toDateString() === today;
      default:
        return true;
    }
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Calendar className="mr-2 h-5 w-5" />
            Booking Requests
          </CardTitle>
          <CardDescription>Manage your booking requests</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse border rounded-lg p-4">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                  <div className="flex-1 space-y-2">
                    <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                    <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const pendingCount = bookings.filter(b => b.status === 'PENDING').length;
  const confirmedCount = bookings.filter(b => b.status === 'CONFIRMED').length;

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Calendar className="mr-2 h-5 w-5" />
              Booking Requests
              {pendingCount > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {pendingCount} pending
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {bookings.length} total requests • {confirmedCount} confirmed
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            {(['all', 'pending', 'confirmed', 'today'] as const).map((filterType) => (
              <Button
                key={filterType}
                variant={filter === filterType ? 'default' : 'outline'}
                size="sm"
                onClick={() => setFilter(filterType)}
              >
                {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        
        {/* Urgent Requests Alert */}
        {bookings.some(b => b.isUrgent && b.status === 'PENDING') && (
          <Alert className="mb-4">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>
              You have urgent booking requests that need immediate attention.
            </AlertDescription>
          </Alert>
        )}

        {filteredBookings.length === 0 ? (
          <div className="text-center py-8">
            <Calendar className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No {filter !== 'all' ? filter : ''} bookings
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter === 'pending' 
                ? 'No pending requests at the moment.' 
                : 'Your booking requests will appear here.'}
            </p>
          </div>
        ) : (
          <div className="space-y-4">
            {filteredBookings.map((booking) => (
              <div 
                key={booking.id} 
                className={`border rounded-lg p-4 ${
                  booking.isUrgent ? 'border-red-200 bg-red-50' : 'hover:bg-gray-50'
                }`}
              >
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <h4 className="text-sm font-medium text-gray-900">
                        {booking.serviceName}
                      </h4>
                      <Badge className={getStatusColor(booking.status)} size="sm">
                        {booking.status}
                      </Badge>
                      {booking.isUrgent && (
                        <Badge variant="destructive" size="sm">
                          Urgent
                        </Badge>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mb-3">
                      <div className="flex items-center">
                        <User className="mr-2 h-3 w-3" />
                        {booking.clientName}
                      </div>
                      <div className="flex items-center">
                        <DollarSign className="mr-2 h-3 w-3" />
                        €{booking.totalPrice}
                      </div>
                      <div className="flex items-center">
                        <Calendar className="mr-2 h-3 w-3" />
                        {formatDate(booking.startTime)}
                      </div>
                      <div className="flex items-center">
                        <Clock className="mr-2 h-3 w-3" />
                        {formatTime(booking.startTime)} - {formatTime(booking.endTime)}
                      </div>
                    </div>

                    {booking.notes && (
                      <p className="text-sm text-gray-600 mb-3 italic">
                        "{booking.notes}"
                      </p>
                    )}

                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center">
                        <Mail className="mr-1 h-3 w-3" />
                        {booking.clientEmail}
                      </div>
                      {booking.clientPhone && (
                        <div className="flex items-center">
                          <Phone className="mr-1 h-3 w-3" />
                          {booking.clientPhone}
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex flex-col space-y-2 ml-4">
                    {booking.status === 'PENDING' && (
                      <>
                        <Button
                          size="sm"
                          onClick={() => handleAcceptBooking(booking.id)}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <Check className="mr-1 h-3 w-3" />
                          Accept
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => handleDeclineBooking(booking.id)}
                          className="text-red-600 border-red-200 hover:bg-red-50"
                        >
                          <X className="mr-1 h-3 w-3" />
                          Decline
                        </Button>
                      </>
                    )}
                    <Button size="sm" variant="ghost">
                      <MessageSquare className="mr-1 h-3 w-3" />
                      Message
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Quick Stats */}
        <div className="mt-6 grid grid-cols-2 md:grid-cols-4 gap-4 pt-4 border-t">
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">{pendingCount}</div>
            <div className="text-xs text-gray-500">Pending</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">{confirmedCount}</div>
            <div className="text-xs text-gray-500">Confirmed</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">
              €{bookings.filter(b => b.status === 'CONFIRMED').reduce((sum, b) => sum + b.totalPrice, 0)}
            </div>
            <div className="text-xs text-gray-500">Confirmed Value</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-gray-900">95%</div>
            <div className="text-xs text-gray-500">Response Rate</div>
          </div>
        </div>

      </CardContent>
    </Card>
  );
}
