'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  TrendingUp, 
  DollarSign, 
  Users, 
  Calendar,
  Star,
  Eye,
  BarChart3,
  PieChart,
  ArrowUp,
  ArrowDown
} from 'lucide-react';

interface AnalyticsData {
  totalRevenue: number;
  totalCommission: number;
  totalBookings: number;
  activeEscorts: number;
  averageRating: number;
  totalProfileViews: number;
  conversionRate: number;
  topPerformers: {
    name: string;
    revenue: number;
    bookings: number;
  }[];
  revenueGrowth: number;
  bookingGrowth: number;
  monthlyData: {
    month: string;
    revenue: number;
    bookings: number;
  }[];
}

export default function AgencyAnalyticsWidget() {
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'quarter' | 'year'>('month');

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setAnalytics({
        totalRevenue: 45600,
        totalCommission: 9120, // 20% commission
        totalBookings: 156,
        activeEscorts: 8,
        averageRating: 4.7,
        totalProfileViews: 12450,
        conversionRate: 12.5,
        revenueGrowth: 15.3,
        bookingGrowth: 8.7,
        topPerformers: [
          { name: 'Sophia', revenue: 15200, bookings: 42 },
          { name: 'Isabella', revenue: 12800, bookings: 38 },
          { name: 'Victoria', revenue: 9600, bookings: 28 },
        ],
        monthlyData: [
          { month: 'Oct', revenue: 38200, bookings: 128 },
          { month: 'Nov', revenue: 42100, bookings: 145 },
          { month: 'Dec', revenue: 45600, bookings: 156 },
        ],
      });
      setIsLoading(false);
    }, 1000);
  }, [timeRange]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <BarChart3 className="mr-2 h-5 w-5" />
            Agency Analytics
          </CardTitle>
          <CardDescription>Performance insights and metrics</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!analytics) return null;

  const kpiItems = [
    {
      label: 'Total Revenue',
      value: `€${analytics.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: analytics.revenueGrowth,
      changeType: 'percentage' as const,
    },
    {
      label: 'Commission Earned',
      value: `€${analytics.totalCommission.toLocaleString()}`,
      icon: TrendingUp,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: analytics.revenueGrowth * 0.8, // Slightly lower than revenue growth
      changeType: 'percentage' as const,
    },
    {
      label: 'Total Bookings',
      value: analytics.totalBookings.toString(),
      icon: Calendar,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: analytics.bookingGrowth,
      changeType: 'percentage' as const,
    },
    {
      label: 'Active Escorts',
      value: analytics.activeEscorts.toString(),
      icon: Users,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: 2,
      changeType: 'absolute' as const,
    },
    {
      label: 'Average Rating',
      value: analytics.averageRating.toFixed(1),
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      change: 0.3,
      changeType: 'absolute' as const,
    },
    {
      label: 'Profile Views',
      value: analytics.totalProfileViews.toLocaleString(),
      icon: Eye,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      change: 22.5,
      changeType: 'percentage' as const,
    },
    {
      label: 'Conversion Rate',
      value: `${analytics.conversionRate}%`,
      icon: TrendingUp,
      color: 'text-teal-600',
      bgColor: 'bg-teal-50',
      change: 1.2,
      changeType: 'percentage' as const,
    },
    {
      label: 'Avg Booking Value',
      value: `€${(analytics.totalRevenue / analytics.totalBookings).toFixed(0)}`,
      icon: DollarSign,
      color: 'text-pink-600',
      bgColor: 'bg-pink-50',
      change: 6.8,
      changeType: 'percentage' as const,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <BarChart3 className="mr-2 h-5 w-5" />
              Agency Analytics
            </CardTitle>
            <CardDescription>Performance insights and key metrics</CardDescription>
          </div>
          <div className="flex space-x-2">
            {(['week', 'month', 'quarter', 'year'] as const).map((range) => (
              <Button
                key={range}
                variant={timeRange === range ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeRange(range)}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Key Performance Indicators */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {kpiItems.map((item) => (
            <div key={item.label} className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className={`p-2 rounded-lg ${item.bgColor}`}>
                  <item.icon className={`h-4 w-4 ${item.color}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {item.label}
                  </p>
                  <div className="flex items-center space-x-2">
                    <p className="text-lg font-bold text-gray-900">
                      {item.value}
                    </p>
                    <div className="flex items-center">
                      {item.change > 0 ? (
                        <ArrowUp className="h-3 w-3 text-green-500" />
                      ) : (
                        <ArrowDown className="h-3 w-3 text-red-500" />
                      )}
                      <span className={`text-xs ${
                        item.change > 0 ? 'text-green-600' : 'text-red-600'
                      }`}>
                        {Math.abs(item.change)}{item.changeType === 'percentage' ? '%' : ''}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Top Performers */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-900">Top Performers This Month</h4>
          <div className="space-y-2">
            {analytics.topPerformers.map((performer, index) => (
              <div key={performer.name} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`w-6 h-6 rounded-full flex items-center justify-center text-xs font-bold ${
                    index === 0 ? 'bg-yellow-100 text-yellow-800' :
                    index === 1 ? 'bg-gray-100 text-gray-800' :
                    'bg-orange-100 text-orange-800'
                  }`}>
                    {index + 1}
                  </div>
                  <span className="font-medium text-gray-900">{performer.name}</span>
                </div>
                <div className="text-right">
                  <div className="text-sm font-medium text-gray-900">
                    €{performer.revenue.toLocaleString()}
                  </div>
                  <div className="text-xs text-gray-500">
                    {performer.bookings} bookings
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Revenue Trend */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-900">Revenue Trend</h4>
          <div className="space-y-2">
            {analytics.monthlyData.map((data, index) => (
              <div key={data.month} className="flex items-center justify-between">
                <span className="text-sm text-gray-600">{data.month}</span>
                <div className="flex items-center space-x-4">
                  <div className="w-32 bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-600 h-2 rounded-full"
                      style={{
                        width: `${(data.revenue / Math.max(...analytics.monthlyData.map(d => d.revenue))) * 100}%`
                      }}
                    />
                  </div>
                  <span className="text-sm font-medium text-gray-900 w-20 text-right">
                    €{data.revenue.toLocaleString()}
                  </span>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Performance Insights */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Performance Insights</h4>
          <div className="space-y-2 text-sm text-blue-800">
            <div className="flex items-center space-x-2">
              <TrendingUp className="h-4 w-4" />
              <span>Revenue is up {analytics.revenueGrowth}% compared to last month</span>
            </div>
            <div className="flex items-center space-x-2">
              <Users className="h-4 w-4" />
              <span>Your top 3 escorts generate {((analytics.topPerformers.reduce((sum, p) => sum + p.revenue, 0) / analytics.totalRevenue) * 100).toFixed(1)}% of total revenue</span>
            </div>
            <div className="flex items-center space-x-2">
              <Star className="h-4 w-4" />
              <span>Average rating improved by 0.3 points this month</span>
            </div>
            <div className="flex items-center space-x-2">
              <PieChart className="h-4 w-4" />
              <span>Conversion rate of {analytics.conversionRate}% is above industry average</span>
            </div>
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2 pt-4 border-t">
          <Button variant="outline" size="sm">
            <BarChart3 className="mr-2 h-4 w-4" />
            Detailed Analytics
          </Button>
          <Button variant="outline" size="sm">
            Export Report
          </Button>
          <Button variant="outline" size="sm">
            Performance Goals
          </Button>
        </div>

      </CardContent>
    </Card>
  );
}
