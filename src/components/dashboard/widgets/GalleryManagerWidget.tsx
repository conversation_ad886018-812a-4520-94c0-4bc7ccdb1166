'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import FileUpload from '@/components/upload/FileUpload';
import { 
  Image as ImageIcon, 
  Video, 
  Upload, 
  Eye, 
  Trash2, 
  Clock,
  CheckCircle,
  XCircle,
  Plus
} from 'lucide-react';

interface MediaItem {
  id: string;
  filename: string;
  fileUrl: string;
  thumbnailUrl?: string;
  type: 'IMAGE' | 'VIDEO';
  status: 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED';
  fileSize: number;
  createdAt: string;
  rejectionNote?: string;
}

export default function GalleryManagerWidget() {
  const [media, setMedia] = useState<MediaItem[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showUpload, setShowUpload] = useState(false);
  const [uploadError, setUploadError] = useState<string | null>(null);

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setMedia([
        {
          id: '1',
          filename: 'profile-photo-1.jpg',
          fileUrl: '/placeholder-image.jpg',
          thumbnailUrl: '/placeholder-thumb.jpg',
          type: 'IMAGE',
          status: 'APPROVED',
          fileSize: 2048000,
          createdAt: '2024-01-15T10:00:00Z',
        },
        {
          id: '2',
          filename: 'gallery-photo-2.jpg',
          fileUrl: '/placeholder-image-2.jpg',
          thumbnailUrl: '/placeholder-thumb-2.jpg',
          type: 'IMAGE',
          status: 'PENDING_APPROVAL',
          fileSize: 1536000,
          createdAt: '2024-01-16T14:30:00Z',
        },
        {
          id: '3',
          filename: 'intro-video.mp4',
          fileUrl: '/placeholder-video.mp4',
          thumbnailUrl: '/placeholder-video-thumb.jpg',
          type: 'VIDEO',
          status: 'REJECTED',
          fileSize: 15728640,
          createdAt: '2024-01-14T16:45:00Z',
          rejectionNote: 'Video quality too low. Please upload in HD.',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'PENDING_APPROVAL':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'REJECTED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'APPROVED':
        return 'bg-green-100 text-green-800';
      case 'PENDING_APPROVAL':
        return 'bg-yellow-100 text-yellow-800';
      case 'REJECTED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const handleDelete = async (mediaId: string) => {
    try {
      const token = localStorage.getItem('token');
      const response = await fetch(`/api/media?id=${mediaId}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
        },
      });

      if (response.ok) {
        setMedia(media.filter(item => item.id !== mediaId));
      } else {
        const error = await response.json();
        setUploadError(error.error || 'Failed to delete media');
      }
    } catch (error) {
      setUploadError('Failed to delete media');
    }
  };

  const handleUploadComplete = (uploadedFiles: any[]) => {
    // Add new files to the media list
    const newMedia = uploadedFiles.map(file => ({
      id: file.id,
      filename: file.filename,
      fileUrl: file.fileUrl,
      thumbnailUrl: file.thumbnailUrl,
      type: file.type === 'image/jpeg' || file.type === 'image/png' || file.type === 'image/webp' ? 'IMAGE' as const : 'VIDEO' as const,
      status: file.status as 'PENDING_APPROVAL' | 'APPROVED' | 'REJECTED',
      fileSize: file.fileSize || 0,
      createdAt: new Date().toISOString(),
    }));

    setMedia(prev => [...newMedia, ...prev]);
    setShowUpload(false);
    setUploadError(null);
  };

  const handleUploadError = (error: string) => {
    setUploadError(error);
  };

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <ImageIcon className="mr-2 h-5 w-5" />
            Gallery Manager
          </CardTitle>
          <CardDescription>Manage your photos and videos</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="aspect-square bg-gray-200 rounded-lg mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-3/4"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const approvedMedia = media.filter(item => item.status === 'APPROVED');
  const pendingMedia = media.filter(item => item.status === 'PENDING_APPROVAL');
  const rejectedMedia = media.filter(item => item.status === 'REJECTED');

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <ImageIcon className="mr-2 h-5 w-5" />
              Gallery Manager
            </CardTitle>
            <CardDescription>
              {media.length} media files • {approvedMedia.length} approved
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button
              onClick={() => setShowUpload(!showUpload)}
              size="sm"
            >
              <Plus className="mr-2 h-4 w-4" />
              Upload
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Upload Section */}
        {showUpload && (
          <div className="border rounded-lg p-4 bg-gray-50">
            <FileUpload
              onUploadComplete={handleUploadComplete}
              onUploadError={handleUploadError}
              maxFiles={5}
              acceptedTypes={['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/webm']}
              maxFileSize={10 * 1024 * 1024} // 10MB
            />
          </div>
        )}

        {/* Upload Error */}
        {uploadError && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription>{uploadError}</AlertDescription>
          </Alert>
        )}

        {/* Status Summary */}
        <div className="grid grid-cols-3 gap-4">
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{approvedMedia.length}</div>
            <div className="text-sm text-green-700">Approved</div>
          </div>
          <div className="text-center p-3 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{pendingMedia.length}</div>
            <div className="text-sm text-yellow-700">Pending</div>
          </div>
          <div className="text-center p-3 bg-red-50 rounded-lg">
            <div className="text-2xl font-bold text-red-600">{rejectedMedia.length}</div>
            <div className="text-sm text-red-700">Rejected</div>
          </div>
        </div>

        {/* Media Grid */}
        {media.length === 0 ? (
          <div className="text-center py-8">
            <ImageIcon className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">No media uploaded</h3>
            <p className="mt-1 text-sm text-gray-500">
              Upload photos and videos to showcase your services.
            </p>
            <div className="mt-6">
              <Button onClick={() => document.getElementById('media-upload')?.click()}>
                <Plus className="mr-2 h-4 w-4" />
                Upload Media
              </Button>
            </div>
          </div>
        ) : (
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {media.map((item) => (
              <div key={item.id} className="relative group">
                <div className="aspect-square bg-gray-100 rounded-lg overflow-hidden">
                  {item.type === 'IMAGE' ? (
                    <img
                      src={item.thumbnailUrl || item.fileUrl}
                      alt={item.filename}
                      className="w-full h-full object-cover"
                    />
                  ) : (
                    <div className="w-full h-full flex items-center justify-center bg-gray-200">
                      <Video className="h-8 w-8 text-gray-500" />
                    </div>
                  )}
                  
                  {/* Status Badge */}
                  <div className="absolute top-2 left-2">
                    <Badge className={getStatusColor(item.status)} size="sm">
                      {getStatusIcon(item.status)}
                      <span className="ml-1 text-xs">
                        {item.status.replace('_', ' ')}
                      </span>
                    </Badge>
                  </div>

                  {/* Action Buttons */}
                  <div className="absolute inset-0 bg-black bg-opacity-50 opacity-0 group-hover:opacity-100 transition-opacity flex items-center justify-center space-x-2">
                    <Button variant="secondary" size="sm">
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button 
                      variant="destructive" 
                      size="sm"
                      onClick={() => handleDelete(item.id)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  </div>
                </div>

                {/* File Info */}
                <div className="mt-2 space-y-1">
                  <p className="text-xs font-medium text-gray-900 truncate">
                    {item.filename}
                  </p>
                  <p className="text-xs text-gray-500">
                    {formatFileSize(item.fileSize)}
                  </p>
                  {item.rejectionNote && (
                    <p className="text-xs text-red-600">
                      {item.rejectionNote}
                    </p>
                  )}
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Guidelines */}
        <div className="bg-blue-50 p-4 rounded-lg">
          <h4 className="text-sm font-medium text-blue-900 mb-2">Upload Guidelines</h4>
          <ul className="text-xs text-blue-800 space-y-1">
            <li>• Images: JPEG, PNG, WebP (max 10MB)</li>
            <li>• Videos: MP4, WebM (max 50MB)</li>
            <li>• High quality content gets approved faster</li>
            <li>• All content is reviewed within 24 hours</li>
          </ul>
        </div>

      </CardContent>
    </Card>
  );
}
