'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  TrendingUp, 
  Eye, 
  Heart, 
  Calendar, 
  Star, 
  DollarSign,
  Users,
  Clock
} from 'lucide-react';

interface ProfileStats {
  totalViews: number;
  totalBookings: number;
  totalEarnings: number;
  averageRating: number;
  totalReviews: number;
  favoriteCount: number;
  responseRate: number;
  profileCompleteness: number;
}

export default function ProfileStatsWidget() {
  const [stats, setStats] = useState<ProfileStats | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [timeRange, setTimeRange] = useState<'week' | 'month' | 'year'>('month');

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setStats({
        totalViews: 1247,
        totalBookings: 23,
        totalEarnings: 6900,
        averageRating: 4.8,
        totalReviews: 18,
        favoriteCount: 89,
        responseRate: 95,
        profileCompleteness: 85,
      });
      setIsLoading(false);
    }, 1000);
  }, [timeRange]);

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="mr-2 h-5 w-5" />
            Profile Statistics
          </CardTitle>
          <CardDescription>Your performance overview</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="animate-pulse">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-6 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!stats) return null;

  const statItems = [
    {
      label: 'Profile Views',
      value: stats.totalViews.toLocaleString(),
      icon: Eye,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      change: '+12%',
      changeType: 'positive' as const,
    },
    {
      label: 'Total Bookings',
      value: stats.totalBookings.toString(),
      icon: Calendar,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      change: '+8%',
      changeType: 'positive' as const,
    },
    {
      label: 'Total Earnings',
      value: `€${stats.totalEarnings.toLocaleString()}`,
      icon: DollarSign,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      change: '+15%',
      changeType: 'positive' as const,
    },
    {
      label: 'Average Rating',
      value: stats.averageRating.toFixed(1),
      icon: Star,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      change: '+0.2',
      changeType: 'positive' as const,
    },
    {
      label: 'Total Reviews',
      value: stats.totalReviews.toString(),
      icon: Users,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      change: '+3',
      changeType: 'positive' as const,
    },
    {
      label: 'Favorites',
      value: stats.favoriteCount.toString(),
      icon: Heart,
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      change: '+7',
      changeType: 'positive' as const,
    },
    {
      label: 'Response Rate',
      value: `${stats.responseRate}%`,
      icon: Clock,
      color: 'text-teal-600',
      bgColor: 'bg-teal-50',
      change: '+2%',
      changeType: 'positive' as const,
    },
    {
      label: 'Profile Complete',
      value: `${stats.profileCompleteness}%`,
      icon: TrendingUp,
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      change: '+5%',
      changeType: 'positive' as const,
    },
  ];

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <TrendingUp className="mr-2 h-5 w-5" />
              Profile Statistics
            </CardTitle>
            <CardDescription>Your performance overview</CardDescription>
          </div>
          <div className="flex space-x-2">
            {(['week', 'month', 'year'] as const).map((range) => (
              <Button
                key={range}
                variant={timeRange === range ? 'default' : 'outline'}
                size="sm"
                onClick={() => setTimeRange(range)}
              >
                {range.charAt(0).toUpperCase() + range.slice(1)}
              </Button>
            ))}
          </div>
        </div>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {statItems.map((item) => (
            <div key={item.label} className="space-y-2">
              <div className="flex items-center space-x-2">
                <div className={`p-2 rounded-lg ${item.bgColor}`}>
                  <item.icon className={`h-4 w-4 ${item.color}`} />
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {item.label}
                  </p>
                  <div className="flex items-center space-x-2">
                    <p className="text-lg font-bold text-gray-900">
                      {item.value}
                    </p>
                    <Badge 
                      variant="outline" 
                      className={`text-xs ${
                        item.changeType === 'positive' 
                          ? 'text-green-600 border-green-200' 
                          : 'text-red-600 border-red-200'
                      }`}
                    >
                      {item.change}
                    </Badge>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Profile Completeness Progress */}
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <div className="flex items-center justify-between mb-2">
            <span className="text-sm font-medium text-gray-700">Profile Completeness</span>
            <span className="text-sm text-gray-500">{stats.profileCompleteness}%</span>
          </div>
          <div className="w-full bg-gray-200 rounded-full h-2">
            <div
              className="bg-blue-600 h-2 rounded-full transition-all duration-300"
              style={{ width: `${stats.profileCompleteness}%` }}
            />
          </div>
          <p className="text-xs text-gray-500 mt-2">
            Complete your profile to increase visibility and bookings
          </p>
        </div>

        {/* Quick Actions */}
        <div className="mt-4 flex flex-wrap gap-2">
          <Button variant="outline" size="sm">
            View Detailed Analytics
          </Button>
          <Button variant="outline" size="sm">
            Export Report
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
