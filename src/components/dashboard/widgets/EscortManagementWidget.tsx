'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { 
  Users, 
  Plus, 
  Edit, 
  Eye, 
  Calendar, 
  DollarSign,
  Star,
  Heart,
  CheckCircle,
  Clock,
  XCircle,
  MoreHorizontal
} from 'lucide-react';
import Link from 'next/link';

interface ManagedEscort {
  id: string;
  name: string;
  alias: string;
  profileImage?: string;
  status: 'ACTIVE' | 'PENDING_APPROVAL' | 'SUSPENDED' | 'INACTIVE';
  isAvailable: boolean;
  hourlyRate: number;
  totalBookings: number;
  totalEarnings: number;
  averageRating: number;
  reviewCount: number;
  favoriteCount: number;
  joinedDate: string;
  lastActive: string;
}

export default function EscortManagementWidget() {
  const [escorts, setEscorts] = useState<ManagedEscort[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [filter, setFilter] = useState<'all' | 'active' | 'pending' | 'inactive'>('all');

  useEffect(() => {
    // Mock data for now - in real app, fetch from API
    setTimeout(() => {
      setEscorts([
        {
          id: '1',
          name: 'Sophia Elite',
          alias: 'Sophia',
          profileImage: '/placeholder-avatar.jpg',
          status: 'ACTIVE',
          isAvailable: true,
          hourlyRate: 300,
          totalBookings: 45,
          totalEarnings: 13500,
          averageRating: 4.8,
          reviewCount: 32,
          favoriteCount: 89,
          joinedDate: '2023-06-15',
          lastActive: '2024-01-20T14:30:00Z',
        },
        {
          id: '2',
          name: 'Isabella Grace',
          alias: 'Isabella',
          profileImage: '/placeholder-avatar-2.jpg',
          status: 'ACTIVE',
          isAvailable: false,
          hourlyRate: 350,
          totalBookings: 38,
          totalEarnings: 11200,
          averageRating: 4.9,
          reviewCount: 28,
          favoriteCount: 67,
          joinedDate: '2023-08-20',
          lastActive: '2024-01-19T18:45:00Z',
        },
        {
          id: '3',
          name: 'Victoria Rose',
          alias: 'Victoria',
          status: 'PENDING_APPROVAL',
          isAvailable: false,
          hourlyRate: 280,
          totalBookings: 0,
          totalEarnings: 0,
          averageRating: 0,
          reviewCount: 0,
          favoriteCount: 0,
          joinedDate: '2024-01-18',
          lastActive: '2024-01-20T10:15:00Z',
        },
      ]);
      setIsLoading(false);
    }, 1000);
  }, []);

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'PENDING_APPROVAL':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'SUSPENDED':
        return <XCircle className="h-4 w-4 text-red-500" />;
      case 'INACTIVE':
        return <XCircle className="h-4 w-4 text-gray-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING_APPROVAL':
        return 'bg-yellow-100 text-yellow-800';
      case 'SUSPENDED':
        return 'bg-red-100 text-red-800';
      case 'INACTIVE':
        return 'bg-gray-100 text-gray-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      month: 'short',
      day: 'numeric',
      year: 'numeric',
    });
  };

  const formatLastActive = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInHours = Math.floor((now.getTime() - date.getTime()) / (1000 * 60 * 60));
    
    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours}h ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    return `${diffInDays}d ago`;
  };

  const filteredEscorts = escorts.filter(escort => {
    switch (filter) {
      case 'active':
        return escort.status === 'ACTIVE';
      case 'pending':
        return escort.status === 'PENDING_APPROVAL';
      case 'inactive':
        return escort.status === 'INACTIVE' || escort.status === 'SUSPENDED';
      default:
        return true;
    }
  });

  if (isLoading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Users className="mr-2 h-5 w-5" />
            Escort Management
          </CardTitle>
          <CardDescription>Manage your escort team</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="animate-pulse flex items-center space-x-4">
                <div className="w-12 h-12 bg-gray-200 rounded-full"></div>
                <div className="flex-1 space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2"></div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  const activeEscorts = escorts.filter(e => e.status === 'ACTIVE');
  const pendingEscorts = escorts.filter(e => e.status === 'PENDING_APPROVAL');
  const totalEarnings = escorts.reduce((sum, e) => sum + e.totalEarnings, 0);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="flex items-center">
              <Users className="mr-2 h-5 w-5" />
              Escort Management
              {pendingEscorts.length > 0 && (
                <Badge variant="destructive" className="ml-2">
                  {pendingEscorts.length} pending
                </Badge>
              )}
            </CardTitle>
            <CardDescription>
              {escorts.length} total escorts • {activeEscorts.length} active • €{totalEarnings.toLocaleString()} total earnings
            </CardDescription>
          </div>
          <div className="flex space-x-2">
            <Button size="sm">
              <Plus className="mr-2 h-4 w-4" />
              Add Escort
            </Button>
          </div>
        </div>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Filter Tabs */}
        <div className="flex space-x-2">
          {(['all', 'active', 'pending', 'inactive'] as const).map((filterType) => (
            <Button
              key={filterType}
              variant={filter === filterType ? 'default' : 'outline'}
              size="sm"
              onClick={() => setFilter(filterType)}
            >
              {filterType.charAt(0).toUpperCase() + filterType.slice(1)}
              {filterType === 'pending' && pendingEscorts.length > 0 && (
                <Badge variant="secondary" className="ml-2">
                  {pendingEscorts.length}
                </Badge>
              )}
            </Button>
          ))}
        </div>

        {/* Quick Stats */}
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          <div className="text-center p-3 bg-green-50 rounded-lg">
            <div className="text-2xl font-bold text-green-600">{activeEscorts.length}</div>
            <div className="text-sm text-green-700">Active</div>
          </div>
          <div className="text-center p-3 bg-yellow-50 rounded-lg">
            <div className="text-2xl font-bold text-yellow-600">{pendingEscorts.length}</div>
            <div className="text-sm text-yellow-700">Pending</div>
          </div>
          <div className="text-center p-3 bg-blue-50 rounded-lg">
            <div className="text-2xl font-bold text-blue-600">
              {escorts.reduce((sum, e) => sum + e.totalBookings, 0)}
            </div>
            <div className="text-sm text-blue-700">Total Bookings</div>
          </div>
          <div className="text-center p-3 bg-purple-50 rounded-lg">
            <div className="text-2xl font-bold text-purple-600">
              {escorts.length > 0 ? (escorts.reduce((sum, e) => sum + e.averageRating, 0) / escorts.length).toFixed(1) : '0'}
            </div>
            <div className="text-sm text-purple-700">Avg Rating</div>
          </div>
        </div>

        {/* Escort List */}
        {filteredEscorts.length === 0 ? (
          <div className="text-center py-8">
            <Users className="mx-auto h-12 w-12 text-gray-400" />
            <h3 className="mt-2 text-sm font-medium text-gray-900">
              No {filter !== 'all' ? filter : ''} escorts
            </h3>
            <p className="mt-1 text-sm text-gray-500">
              {filter === 'all' 
                ? 'Add your first escort to get started.' 
                : `No ${filter} escorts at the moment.`}
            </p>
            {filter === 'all' && (
              <div className="mt-6">
                <Button>
                  <Plus className="mr-2 h-4 w-4" />
                  Add Escort
                </Button>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-4">
            {filteredEscorts.map((escort) => (
              <div key={escort.id} className="border rounded-lg p-4 hover:bg-gray-50">
                <div className="flex items-center space-x-4">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={escort.profileImage} alt={escort.name} />
                    <AvatarFallback>{escort.alias.charAt(0)}</AvatarFallback>
                  </Avatar>
                  
                  <div className="flex-1 min-w-0">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="text-sm font-medium text-gray-900 truncate">
                        {escort.alias}
                      </h4>
                      <Badge className={getStatusColor(escort.status)} size="sm">
                        {getStatusIcon(escort.status)}
                        <span className="ml-1">{escort.status.replace('_', ' ')}</span>
                      </Badge>
                      {escort.isAvailable && escort.status === 'ACTIVE' && (
                        <Badge variant="outline" size="sm" className="text-green-600">
                          Available
                        </Badge>
                      )}
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                      <div className="flex items-center">
                        <DollarSign className="mr-1 h-3 w-3" />
                        €{escort.hourlyRate}/hr
                      </div>
                      <div className="flex items-center">
                        <Calendar className="mr-1 h-3 w-3" />
                        {escort.totalBookings} bookings
                      </div>
                      <div className="flex items-center">
                        <Star className="mr-1 h-3 w-3" />
                        {escort.averageRating > 0 ? escort.averageRating.toFixed(1) : 'No ratings'}
                      </div>
                      <div className="flex items-center">
                        <Heart className="mr-1 h-3 w-3" />
                        {escort.favoriteCount} favorites
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between mt-2 text-xs text-gray-500">
                      <span>Joined {formatDate(escort.joinedDate)}</span>
                      <span>Last active {formatLastActive(escort.lastActive)}</span>
                    </div>
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center space-x-2">
                    <Link href={`/dashboard/escorts/${escort.id}`}>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Link href={`/dashboard/escorts/${escort.id}/edit`}>
                      <Button variant="ghost" size="sm">
                        <Edit className="h-4 w-4" />
                      </Button>
                    </Link>
                    <Button variant="ghost" size="sm">
                      <MoreHorizontal className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex flex-wrap gap-2 pt-4 border-t">
          <Button variant="outline" size="sm">
            Export Report
          </Button>
          <Button variant="outline" size="sm">
            Bulk Actions
          </Button>
          <Button variant="outline" size="sm">
            Performance Analytics
          </Button>
        </div>

      </CardContent>
    </Card>
  );
}
