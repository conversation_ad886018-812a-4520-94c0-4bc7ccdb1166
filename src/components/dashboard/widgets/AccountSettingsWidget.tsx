'use client';

import { useState } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Settings, 
  User, 
  Mail, 
  Shield, 
  Bell, 
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Clock
} from 'lucide-react';
import Link from 'next/link';

export default function AccountSettingsWidget() {
  const { user } = useAuth();
  const [isResendingVerification, setIsResendingVerification] = useState(false);

  if (!user) return null;

  const handleResendVerification = async () => {
    setIsResendingVerification(true);
    // Mock API call - in real app, call verification API
    setTimeout(() => {
      setIsResendingVerification(false);
      // Show success message
    }, 2000);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'PENDING_VERIFICATION':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'SUSPENDED':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      default:
        return <AlertTriangle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE':
        return 'bg-green-100 text-green-800';
      case 'PENDING_VERIFICATION':
        return 'bg-yellow-100 text-yellow-800';
      case 'SUSPENDED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center">
          <Settings className="mr-2 h-5 w-5" />
          Account Settings
        </CardTitle>
        <CardDescription>Manage your account and preferences</CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        
        {/* Account Status */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-900">Account Status</h4>
          <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center space-x-3">
              {getStatusIcon(user.status)}
              <div>
                <p className="text-sm font-medium text-gray-900">Account Status</p>
                <Badge className={getStatusColor(user.status)} size="sm">
                  {user.status.replace('_', ' ')}
                </Badge>
              </div>
            </div>
          </div>
        </div>

        {/* Email Verification */}
        {!user.emailVerified && (
          <Alert>
            <Mail className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>Please verify your email address to access all features.</span>
              <Button
                variant="outline"
                size="sm"
                onClick={handleResendVerification}
                disabled={isResendingVerification}
              >
                {isResendingVerification ? 'Sending...' : 'Resend'}
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Quick Settings */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-900">Quick Settings</h4>
          <div className="space-y-2">
            
            <Link href="/dashboard/settings/profile">
              <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <div className="flex items-center space-x-3">
                  <User className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Profile Settings</p>
                    <p className="text-xs text-gray-500">Update your personal information</p>
                  </div>
                </div>
                <Badge variant="outline" size="sm">Edit</Badge>
              </div>
            </Link>

            <Link href="/dashboard/settings/security">
              <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <div className="flex items-center space-x-3">
                  <Shield className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Security</p>
                    <p className="text-xs text-gray-500">Password and security settings</p>
                  </div>
                </div>
                <Badge variant="outline" size="sm">Manage</Badge>
              </div>
            </Link>

            <Link href="/dashboard/settings/notifications">
              <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                <div className="flex items-center space-x-3">
                  <Bell className="h-4 w-4 text-gray-500" />
                  <div>
                    <p className="text-sm font-medium text-gray-900">Notifications</p>
                    <p className="text-xs text-gray-500">Email and push notification preferences</p>
                  </div>
                </div>
                <Badge variant="outline" size="sm">Configure</Badge>
              </div>
            </Link>

            {user.role === 'MEMBER' && (
              <Link href="/dashboard/settings/billing">
                <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
                  <div className="flex items-center space-x-3">
                    <CreditCard className="h-4 w-4 text-gray-500" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">Billing & Payments</p>
                      <p className="text-xs text-gray-500">Payment methods and billing history</p>
                    </div>
                  </div>
                  <Badge variant="outline" size="sm">Manage</Badge>
                </div>
              </Link>
            )}

          </div>
        </div>

        {/* Account Info Summary */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium text-gray-900">Account Information</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-500">Username:</span>
              <span className="font-medium">{user.username}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Email:</span>
              <span className="font-medium">{user.email}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Role:</span>
              <span className="font-medium">{user.role}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-500">Member since:</span>
              <span className="font-medium">
                {new Date(user.createdAt).toLocaleDateString()}
              </span>
            </div>
          </div>
        </div>

        {/* Action Buttons */}
        <div className="pt-4 border-t space-y-2">
          <Link href="/dashboard/settings">
            <Button variant="outline" className="w-full">
              View All Settings
            </Button>
          </Link>
        </div>

      </CardContent>
    </Card>
  );
}
