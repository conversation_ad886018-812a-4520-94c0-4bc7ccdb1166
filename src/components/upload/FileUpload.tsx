'use client';

import { useState, useRef, useCallback } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { Button } from '@/components/ui/button';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { 
  Upload, 
  X, 
  Image as ImageIcon, 
  Video, 
  CheckCircle,
  AlertTriangle,
  Loader2
} from 'lucide-react';

interface FileUploadProps {
  onUploadComplete?: (files: UploadedFile[]) => void;
  onUploadError?: (error: string) => void;
  maxFiles?: number;
  acceptedTypes?: string[];
  maxFileSize?: number; // in bytes
  className?: string;
}

interface UploadedFile {
  id: string;
  filename: string;
  fileUrl: string;
  thumbnailUrl?: string;
  type: string;
  status: string;
}

interface UploadProgress {
  file: File;
  progress: number;
  status: 'uploading' | 'success' | 'error';
  error?: string;
  result?: UploadedFile;
}

export default function FileUpload({
  onUploadComplete,
  onUploadError,
  maxFiles = 10,
  acceptedTypes = ['image/jpeg', 'image/png', 'image/webp', 'video/mp4', 'video/webm'],
  maxFileSize = 10 * 1024 * 1024, // 10MB
  className = '',
}: FileUploadProps) {
  const { user } = useAuth();
  const [isDragOver, setIsDragOver] = useState(false);
  const [uploads, setUploads] = useState<UploadProgress[]>([]);
  const [isUploading, setIsUploading] = useState(false);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const validateFile = (file: File): string | null => {
    if (!acceptedTypes.includes(file.type)) {
      return `File type ${file.type} is not allowed`;
    }
    
    if (file.size > maxFileSize) {
      const maxSizeMB = maxFileSize / (1024 * 1024);
      return `File size exceeds ${maxSizeMB}MB limit`;
    }
    
    return null;
  };

  const uploadFile = async (file: File): Promise<UploadedFile> => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('type', 'gallery');

    const token = localStorage.getItem('token');
    const response = await fetch('/api/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${token}`,
      },
      body: formData,
    });

    if (!response.ok) {
      const error = await response.json();
      throw new Error(error.error || 'Upload failed');
    }

    const result = await response.json();
    return result.data;
  };

  const handleFiles = useCallback(async (files: FileList) => {
    if (!user) {
      onUploadError?.('Please log in to upload files');
      return;
    }

    const fileArray = Array.from(files);
    
    if (fileArray.length > maxFiles) {
      onUploadError?.(`Maximum ${maxFiles} files allowed`);
      return;
    }

    // Validate all files first
    const validationErrors: string[] = [];
    fileArray.forEach((file, index) => {
      const error = validateFile(file);
      if (error) {
        validationErrors.push(`File ${index + 1}: ${error}`);
      }
    });

    if (validationErrors.length > 0) {
      onUploadError?(validationErrors.join('\n'));
      return;
    }

    // Initialize upload progress
    const initialUploads: UploadProgress[] = fileArray.map(file => ({
      file,
      progress: 0,
      status: 'uploading' as const,
    }));

    setUploads(initialUploads);
    setIsUploading(true);

    // Upload files sequentially
    const results: UploadedFile[] = [];
    
    for (let i = 0; i < fileArray.length; i++) {
      const file = fileArray[i];
      
      try {
        // Update progress to show starting
        setUploads(prev => prev.map((upload, index) => 
          index === i ? { ...upload, progress: 10 } : upload
        ));

        const result = await uploadFile(file);
        
        // Update progress to show success
        setUploads(prev => prev.map((upload, index) => 
          index === i ? { 
            ...upload, 
            progress: 100, 
            status: 'success' as const,
            result 
          } : upload
        ));

        results.push(result);

      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Upload failed';
        
        // Update progress to show error
        setUploads(prev => prev.map((upload, index) => 
          index === i ? { 
            ...upload, 
            progress: 0, 
            status: 'error' as const,
            error: errorMessage 
          } : upload
        ));

        onUploadError?.(errorMessage);
      }
    }

    setIsUploading(false);
    
    if (results.length > 0) {
      onUploadComplete?.(results);
    }

    // Clear uploads after a delay
    setTimeout(() => {
      setUploads([]);
    }, 3000);

  }, [user, maxFiles, maxFileSize, acceptedTypes, onUploadComplete, onUploadError]);

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFiles(files);
    }
  }, [handleFiles]);

  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(true);
  }, []);

  const handleDragLeave = useCallback((e: React.DragEvent) => {
    e.preventDefault();
    setIsDragOver(false);
  }, []);

  const handleFileSelect = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (files && files.length > 0) {
      handleFiles(files);
    }
    // Reset input
    if (fileInputRef.current) {
      fileInputRef.current.value = '';
    }
  }, [handleFiles]);

  const removeUpload = (index: number) => {
    setUploads(prev => prev.filter((_, i) => i !== index));
  };

  const getFileIcon = (file: File) => {
    if (file.type.startsWith('image/')) {
      return <ImageIcon className="h-8 w-8 text-blue-500" />;
    } else if (file.type.startsWith('video/')) {
      return <Video className="h-8 w-8 text-purple-500" />;
    }
    return <Upload className="h-8 w-8 text-gray-500" />;
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-5 w-5 text-green-500" />;
      case 'error':
        return <AlertTriangle className="h-5 w-5 text-red-500" />;
      case 'uploading':
        return <Loader2 className="h-5 w-5 text-blue-500 animate-spin" />;
      default:
        return null;
    }
  };

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Upload Area */}
      <div
        className={`border-2 border-dashed rounded-lg p-8 text-center transition-colors ${
          isDragOver
            ? 'border-blue-500 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        } ${isUploading ? 'pointer-events-none opacity-50' : ''}`}
        onDrop={handleDrop}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
      >
        <Upload className="mx-auto h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Upload Files
        </h3>
        <p className="text-sm text-gray-600 mb-4">
          Drag and drop files here, or click to select files
        </p>
        <Button
          onClick={() => fileInputRef.current?.click()}
          disabled={isUploading}
        >
          Select Files
        </Button>
        
        <input
          ref={fileInputRef}
          type="file"
          multiple
          accept={acceptedTypes.join(',')}
          onChange={handleFileSelect}
          className="hidden"
        />
        
        <div className="mt-4 text-xs text-gray-500">
          <p>Supported formats: {acceptedTypes.join(', ')}</p>
          <p>Maximum file size: {(maxFileSize / (1024 * 1024)).toFixed(1)}MB</p>
          <p>Maximum files: {maxFiles}</p>
        </div>
      </div>

      {/* Upload Progress */}
      {uploads.length > 0 && (
        <div className="space-y-3">
          <h4 className="font-medium text-gray-900">Upload Progress</h4>
          {uploads.map((upload, index) => (
            <div key={index} className="border rounded-lg p-4">
              <div className="flex items-center space-x-3">
                {getFileIcon(upload.file)}
                <div className="flex-1 min-w-0">
                  <p className="text-sm font-medium text-gray-900 truncate">
                    {upload.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {(upload.file.size / (1024 * 1024)).toFixed(2)} MB
                  </p>
                  
                  {upload.status === 'uploading' && (
                    <Progress value={upload.progress} className="mt-2" />
                  )}
                  
                  {upload.error && (
                    <Alert className="mt-2">
                      <AlertTriangle className="h-4 w-4" />
                      <AlertDescription className="text-xs">
                        {upload.error}
                      </AlertDescription>
                    </Alert>
                  )}
                </div>
                
                <div className="flex items-center space-x-2">
                  {getStatusIcon(upload.status)}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => removeUpload(index)}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
