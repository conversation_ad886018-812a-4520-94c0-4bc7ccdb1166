import { NextRequest, NextResponse } from 'next/server';
import { verifyToken } from '@/lib/jwt';
import { UserRole } from '@/types';

export interface AuthenticatedRequest extends NextRequest {
  user?: {
    userId: string;
    email: string;
    role: UserRole;
  };
}

export function withAuth(
  handler: (req: AuthenticatedRequest) => Promise<NextResponse>,
  requiredRoles?: UserRole[]
) {
  return async (req: NextRequest): Promise<NextResponse> => {
    try {
      // Get token from Authorization header
      const authHeader = req.headers.get('authorization');
      const token = authHeader?.replace('Bearer ', '');

      if (!token) {
        return NextResponse.json(
          { success: false, error: 'No token provided' },
          { status: 401 }
        );
      }

      // Verify token
      const payload = verifyToken(token);
      if (!payload) {
        return NextResponse.json(
          { success: false, error: 'Invalid token' },
          { status: 401 }
        );
      }

      // Check role permissions
      if (requiredRoles && !requiredRoles.includes(payload.role)) {
        return NextResponse.json(
          { success: false, error: 'Insufficient permissions' },
          { status: 403 }
        );
      }

      // Add user to request
      (req as AuthenticatedRequest).user = {
        userId: payload.userId,
        email: payload.email,
        role: payload.role,
      };

      return handler(req as AuthenticatedRequest);
    } catch (error) {
      console.error('Auth middleware error:', error);
      return NextResponse.json(
        { success: false, error: 'Authentication failed' },
        { status: 500 }
      );
    }
  };
}

export function requireRole(...roles: UserRole[]) {
  return (handler: (req: AuthenticatedRequest) => Promise<NextResponse>) =>
    withAuth(handler, roles);
}

export const requireAdmin = requireRole(UserRole.ADMIN);
export const requireEscort = requireRole(UserRole.ESCORT);
export const requireAgency = requireRole(UserRole.AGENCY);
export const requireStudio = requireRole(UserRole.STUDIO);
export const requireMember = requireRole(UserRole.MEMBER);

// Middleware for multiple roles
export const requireProvider = requireRole(UserRole.ESCORT, UserRole.AGENCY, UserRole.STUDIO);
export const requireManager = requireRole(UserRole.AGENCY, UserRole.STUDIO, UserRole.ADMIN);
