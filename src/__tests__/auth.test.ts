import { describe, it, expect, beforeEach, afterEach } from '@jest/globals';
import { NextRequest } from 'next/server';
import { POST as loginHandler } from '@/app/api/auth/login/route';
import { POST as registerHandler } from '@/app/api/auth/register/route';
import { prisma } from '@/lib/prisma';
import { hashPassword } from '@/middleware/security';

// Mock data
const mockUser = {
  username: 'testuser',
  email: '<EMAIL>',
  password: 'TestPassword123!',
  role: 'MEMBER' as const,
};

const mockEscortUser = {
  username: 'testescort',
  email: '<EMAIL>',
  password: 'EscortPassword123!',
  role: 'ESCORT' as const,
  profileData: {
    name: 'Test Escort',
    alias: 'Test<PERSON><PERSON><PERSON>',
    description: 'Test description',
    location: 'Test City',
    profileType: 'ESCORT' as const,
  },
};

// Helper function to create mock request
function createMockRequest(body: any, method: string = 'POST'): NextRequest {
  return new NextRequest('http://localhost:3000/api/test', {
    method,
    headers: {
      'Content-Type': 'application/json',
    },
    body: JSON.stringify(body),
  });
}

describe('Authentication API', () => {
  beforeEach(async () => {
    // Clean up test data
    await prisma.user.deleteMany({
      where: {
        email: {
          in: [mockUser.email, mockEscortUser.email],
        },
      },
    });
  });

  afterEach(async () => {
    // Clean up test data
    await prisma.user.deleteMany({
      where: {
        email: {
          in: [mockUser.email, mockEscortUser.email],
        },
      },
    });
  });

  describe('POST /api/auth/register', () => {
    it('should register a new member user successfully', async () => {
      const request = createMockRequest(mockUser);
      const response = await registerHandler(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.user.email).toBe(mockUser.email);
      expect(data.data.user.role).toBe(mockUser.role);
      expect(data.data.token).toBeDefined();

      // Verify user was created in database
      const dbUser = await prisma.user.findUnique({
        where: { email: mockUser.email },
      });
      expect(dbUser).toBeTruthy();
      expect(dbUser?.username).toBe(mockUser.username);
    });

    it('should register a new escort user with profile', async () => {
      const request = createMockRequest(mockEscortUser);
      const response = await registerHandler(request);
      const data = await response.json();

      expect(response.status).toBe(201);
      expect(data.success).toBe(true);
      expect(data.data.user.role).toBe('ESCORT');

      // Verify profile was created
      const dbUser = await prisma.user.findUnique({
        where: { email: mockEscortUser.email },
        include: { profile: true },
      });
      expect(dbUser?.profile).toBeTruthy();
      expect(dbUser?.profile?.name).toBe(mockEscortUser.profileData.name);
    });

    it('should reject registration with invalid email', async () => {
      const invalidUser = { ...mockUser, email: 'invalid-email' };
      const request = createMockRequest(invalidUser);
      const response = await registerHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('email');
    });

    it('should reject registration with weak password', async () => {
      const weakPasswordUser = { ...mockUser, password: '123' };
      const request = createMockRequest(weakPasswordUser);
      const response = await registerHandler(request);
      const data = await response.json();

      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error).toContain('password');
    });

    it('should reject duplicate email registration', async () => {
      // First registration
      const request1 = createMockRequest(mockUser);
      await registerHandler(request1);

      // Second registration with same email
      const request2 = createMockRequest(mockUser);
      const response = await registerHandler(request2);
      const data = await response.json();

      expect(response.status).toBe(409);
      expect(data.success).toBe(false);
      expect(data.error).toContain('already exists');
    });
  });

  describe('POST /api/auth/login', () => {
    beforeEach(async () => {
      // Create test user
      const hashedPassword = await hashPassword(mockUser.password);
      await prisma.user.create({
        data: {
          username: mockUser.username,
          email: mockUser.email,
          password: hashedPassword,
          role: mockUser.role,
          emailVerified: true,
          status: 'ACTIVE',
        },
      });
    });

    it('should login with valid credentials', async () => {
      const loginData = {
        email: mockUser.email,
        password: mockUser.password,
      };
      const request = createMockRequest(loginData);
      const response = await loginHandler(request);
      const data = await response.json();

      expect(response.status).toBe(200);
      expect(data.success).toBe(true);
      expect(data.data.user.email).toBe(mockUser.email);
      expect(data.data.token).toBeDefined();
    });

    it('should reject login with invalid email', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: mockUser.password,
      };
      const request = createMockRequest(loginData);
      const response = await loginHandler(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid credentials');
    });

    it('should reject login with invalid password', async () => {
      const loginData = {
        email: mockUser.email,
        password: 'wrongpassword',
      };
      const request = createMockRequest(loginData);
      const response = await loginHandler(request);
      const data = await response.json();

      expect(response.status).toBe(401);
      expect(data.success).toBe(false);
      expect(data.error).toContain('Invalid credentials');
    });

    it('should reject login for unverified email', async () => {
      // Create unverified user
      const hashedPassword = await hashPassword('TestPassword123!');
      await prisma.user.create({
        data: {
          username: 'unverified',
          email: '<EMAIL>',
          password: hashedPassword,
          role: 'MEMBER',
          emailVerified: false,
          status: 'ACTIVE',
        },
      });

      const loginData = {
        email: '<EMAIL>',
        password: 'TestPassword123!',
      };
      const request = createMockRequest(loginData);
      const response = await loginHandler(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.success).toBe(false);
      expect(data.error).toContain('email verification');
    });

    it('should reject login for suspended user', async () => {
      // Update user status to suspended
      await prisma.user.update({
        where: { email: mockUser.email },
        data: { status: 'SUSPENDED' },
      });

      const loginData = {
        email: mockUser.email,
        password: mockUser.password,
      };
      const request = createMockRequest(loginData);
      const response = await loginHandler(request);
      const data = await response.json();

      expect(response.status).toBe(403);
      expect(data.success).toBe(false);
      expect(data.error).toContain('suspended');
    });
  });

  describe('Rate Limiting', () => {
    it('should enforce rate limiting on login attempts', async () => {
      const loginData = {
        email: '<EMAIL>',
        password: 'wrongpassword',
      };

      // Make multiple failed login attempts
      const requests = Array(6).fill(null).map(() => 
        createMockRequest(loginData)
      );

      const responses = await Promise.all(
        requests.map(req => loginHandler(req))
      );

      // First 5 should be 401 (invalid credentials)
      for (let i = 0; i < 5; i++) {
        expect(responses[i].status).toBe(401);
      }

      // 6th should be 429 (rate limited)
      expect(responses[5].status).toBe(429);
    });
  });

  describe('Input Validation', () => {
    it('should sanitize input data', async () => {
      const maliciousUser = {
        username: '<script>alert("xss")</script>',
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: 'MEMBER',
      };

      const request = createMockRequest(maliciousUser);
      const response = await registerHandler(request);
      const data = await response.json();

      if (response.status === 201) {
        // If registration succeeds, username should be sanitized
        const dbUser = await prisma.user.findUnique({
          where: { email: maliciousUser.email },
        });
        expect(dbUser?.username).not.toContain('<script>');
      }
    });

    it('should reject SQL injection attempts', async () => {
      const sqlInjectionUser = {
        username: "'; DROP TABLE users; --",
        email: '<EMAIL>',
        password: 'TestPassword123!',
        role: 'MEMBER',
      };

      const request = createMockRequest(sqlInjectionUser);
      const response = await registerHandler(request);
      
      // Should either reject the input or sanitize it
      expect(response.status).not.toBe(500);
    });
  });
});

describe('JWT Token Handling', () => {
  it('should generate valid JWT tokens', async () => {
    const { generateToken, verifyToken } = await import('@/lib/jwt');
    
    const payload = {
      userId: 'test-user-id',
      email: '<EMAIL>',
      role: 'MEMBER',
    };

    const token = generateToken(payload);
    expect(token).toBeDefined();
    expect(typeof token).toBe('string');

    const decoded = verifyToken(token);
    expect(decoded).toBeTruthy();
    expect(decoded?.userId).toBe(payload.userId);
    expect(decoded?.email).toBe(payload.email);
    expect(decoded?.role).toBe(payload.role);
  });

  it('should reject expired tokens', async () => {
    const { generateToken, verifyToken } = await import('@/lib/jwt');
    
    const payload = {
      userId: 'test-user-id',
      email: '<EMAIL>',
      role: 'MEMBER',
    };

    // Generate token with very short expiry
    const token = generateToken(payload, '1ms');
    
    // Wait for token to expire
    await new Promise(resolve => setTimeout(resolve, 10));
    
    const decoded = verifyToken(token);
    expect(decoded).toBeNull();
  });

  it('should reject tampered tokens', async () => {
    const { generateToken, verifyToken } = await import('@/lib/jwt');
    
    const payload = {
      userId: 'test-user-id',
      email: '<EMAIL>',
      role: 'MEMBER',
    };

    const token = generateToken(payload);
    const tamperedToken = token.slice(0, -5) + 'xxxxx';
    
    const decoded = verifyToken(tamperedToken);
    expect(decoded).toBeNull();
  });
});
