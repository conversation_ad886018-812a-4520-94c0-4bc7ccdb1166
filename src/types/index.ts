// Re-export Prisma types
export type {
  User,
  Profile,
  Media,
  Service,
  Booking,
  Review,
  Favorite,
  Page,
  Setting,
  UserRole,
  UserStatus,
  ProfileStatus,
  MediaStatus,
  MediaType,
  BookingStatus,
  Gender,
} from '@prisma/client';

// Extended types for API responses
export interface UserWithProfile extends User {
  profile?: Profile | null;
}

export interface ProfileWithMedia extends Profile {
  media: Media[];
  services: Service[];
  reviews: Review[];
  _count?: {
    reviews: number;
    favorites: number;
  };
}

export interface BookingWithDetails extends Booking {
  client: User;
  profile: Profile;
  service?: Service | null;
  review?: Review | null;
}

// Form types
export interface RegisterFormData {
  username: string;
  email: string;
  password: string;
  confirmPassword: string;
  role: UserRole;
  agreeToTerms: boolean;
}

export interface ProfileFormData {
  name: string;
  alias?: string;
  location: string;
  description?: string;
  phone?: string;
  website?: string;
  age?: number;
  gender?: Gender;
  height?: number;
  weight?: number;
  languages: string[];
  hourlyRate?: number;
}

export interface ServiceFormData {
  name: string;
  description?: string;
  price: number;
  duration: number;
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Search and filter types
export interface ProfileSearchFilters {
  location?: string;
  gender?: Gender;
  ageMin?: number;
  ageMax?: number;
  priceMin?: number;
  priceMax?: number;
  languages?: string[];
  services?: string[];
  isAvailable?: boolean;
}

export interface SearchParams {
  query?: string;
  filters?: ProfileSearchFilters;
  page?: number;
  limit?: number;
  sortBy?: 'name' | 'price' | 'rating' | 'createdAt';
  sortOrder?: 'asc' | 'desc';
}

// Dashboard widget types
export interface DashboardWidget {
  id: string;
  type: string;
  title: string;
  position: { x: number; y: number };
  size: { width: number; height: number };
  data?: any;
}

// Statistics types
export interface ProfileStats {
  totalViews: number;
  totalBookings: number;
  totalEarnings: number;
  averageRating: number;
  totalReviews: number;
  favoriteCount: number;
}

export interface AdminStats {
  totalUsers: number;
  totalProfiles: number;
  pendingApprovals: number;
  totalBookings: number;
  totalRevenue: number;
  newRegistrationsToday: number;
}

// File upload types
export interface UploadedFile {
  id: string;
  filename: string;
  originalName: string;
  fileUrl: string;
  thumbnailUrl?: string;
  fileSize: number;
  mimeType: string;
  type: MediaType;
}

// Notification types
export interface Notification {
  id: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
}

// JWT Payload type
export interface JWTPayload {
  userId: string;
  email: string;
  role: UserRole;
  iat?: number;
  exp?: number;
}

// Moderation types
export interface ModerationItem {
  id: string;
  type: 'profile' | 'media' | 'review';
  itemId: string;
  submittedBy: string;
  submittedAt: Date;
  status: 'pending' | 'approved' | 'rejected';
  moderatedBy?: string;
  moderatedAt?: Date;
  rejectionNote?: string;
}

// Calendar/Availability types
export interface AvailabilitySlot {
  id: string;
  date: Date;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  isBooked: boolean;
}

export interface CalendarEvent {
  id: string;
  title: string;
  start: Date;
  end: Date;
  type: 'booking' | 'blocked' | 'available';
  bookingId?: string;
}
