import { PrismaClient, UserRole, UserStatus, ProfileStatus, Gender } from '@prisma/client';
import { hashPassword } from '../src/lib/password';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 Starting seed...');

  // Create admin user
  const adminPassword = await hashPassword('Admin123!');
  const admin = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'admin',
      email: '<EMAIL>',
      password: adminPassword,
      role: UserRole.ADMIN,
      status: UserStatus.ACTIVE,
      emailVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  console.log('✅ Admin user created:', admin.email);

  // Create test member
  const memberPassword = await hashPassword('Member123!');
  const member = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'testmember',
      email: '<EMAIL>',
      password: memberPassword,
      role: UserRole.MEMBER,
      status: UserStatus.ACTIVE,
      emailVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  console.log('✅ Test member created:', member.email);

  // Create test escort
  const escortPassword = await hashPassword('Escort123!');
  const escort = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'testescort',
      email: '<EMAIL>',
      password: escortPassword,
      role: UserRole.ESCORT,
      status: UserStatus.ACTIVE,
      emailVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  // Create escort profile
  const escortProfile = await prisma.profile.upsert({
    where: { userId: escort.id },
    update: {},
    create: {
      userId: escort.id,
      profileType: UserRole.ESCORT,
      status: ProfileStatus.APPROVED,
      name: 'Sophia Elite',
      alias: 'Sophia',
      location: 'Berlin, Germany',
      description: 'Professional and discreet companion offering premium services.',
      age: 25,
      gender: Gender.FEMALE,
      height: 170,
      weight: 55,
      languages: ['German', 'English', 'French'],
      hourlyRate: 300,
      isAvailable: true,
      moderatedBy: admin.id,
      moderatedAt: new Date(),
    },
  });

  console.log('✅ Test escort profile created:', escortProfile.name);

  // Create test agency
  const agencyPassword = await hashPassword('Agency123!');
  const agency = await prisma.user.upsert({
    where: { email: '<EMAIL>' },
    update: {},
    create: {
      username: 'testagency',
      email: '<EMAIL>',
      password: agencyPassword,
      role: UserRole.AGENCY,
      status: UserStatus.ACTIVE,
      emailVerified: true,
      emailVerifiedAt: new Date(),
    },
  });

  // Create agency profile
  const agencyProfile = await prisma.profile.upsert({
    where: { userId: agency.id },
    update: {},
    create: {
      userId: agency.id,
      profileType: UserRole.AGENCY,
      status: ProfileStatus.APPROVED,
      name: 'Elite Companions Agency',
      location: 'Berlin, Germany',
      description: 'Premium escort agency providing high-class companions.',
      companyName: 'Elite Companions GmbH',
      website: 'https://elite-companions.com',
      phone: '+49 30 12345678',
      isAvailable: true,
      moderatedBy: admin.id,
      moderatedAt: new Date(),
    },
  });

  console.log('✅ Test agency profile created:', agencyProfile.name);

  // Create services for escort
  const services = await Promise.all([
    prisma.service.create({
      data: {
        profileId: escortProfile.id,
        name: 'Dinner Date',
        description: 'Elegant companion for dinner and social events',
        price: 400,
        duration: 180, // 3 hours
      },
    }),
    prisma.service.create({
      data: {
        profileId: escortProfile.id,
        name: 'Overnight Companion',
        description: 'Full night companionship service',
        price: 1200,
        duration: 720, // 12 hours
      },
    }),
  ]);

  console.log('✅ Services created:', services.length);

  // Create some CMS pages
  const pages = await Promise.all([
    prisma.page.upsert({
      where: { slug: 'terms-of-service' },
      update: {},
      create: {
        slug: 'terms-of-service',
        title: 'Terms of Service',
        content: '# Terms of Service\n\nWelcome to TheGND platform...',
        isPublished: true,
      },
    }),
    prisma.page.upsert({
      where: { slug: 'privacy-policy' },
      update: {},
      create: {
        slug: 'privacy-policy',
        title: 'Privacy Policy',
        content: '# Privacy Policy\n\nYour privacy is important to us...',
        isPublished: true,
      },
    }),
    prisma.page.upsert({
      where: { slug: 'faq' },
      update: {},
      create: {
        slug: 'faq',
        title: 'Frequently Asked Questions',
        content: '# FAQ\n\n## How do I register?\n\nTo register...',
        isPublished: true,
      },
    }),
  ]);

  console.log('✅ CMS pages created:', pages.length);

  // Create some settings
  const settings = await Promise.all([
    prisma.setting.upsert({
      where: { key: 'site_name' },
      update: {},
      create: {
        key: 'site_name',
        value: 'TheGND',
      },
    }),
    prisma.setting.upsert({
      where: { key: 'site_description' },
      update: {},
      create: {
        key: 'site_description',
        value: 'Premium Escort Platform',
      },
    }),
    prisma.setting.upsert({
      where: { key: 'max_file_size' },
      update: {},
      create: {
        key: 'max_file_size',
        value: '10485760', // 10MB
      },
    }),
  ]);

  console.log('✅ Settings created:', settings.length);

  console.log('🎉 Seed completed successfully!');
}

main()
  .catch((e) => {
    console.error('❌ Seed failed:', e);
    process.exit(1);
  })
  .finally(async () => {
    await prisma.$disconnect();
  });
