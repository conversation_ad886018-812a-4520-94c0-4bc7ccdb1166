// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Enums
enum UserRole {
  MEMBER
  ESCORT
  AGENCY
  STUDIO
  ADMIN
}

enum UserStatus {
  PENDING_VERIFICATION
  ACTIVE
  SUSPENDED
  BANNED
}

enum ProfileStatus {
  PENDING_APPROVAL
  APPROVED
  REJECTED
  SUSPENDED
}

enum MediaStatus {
  PENDING_APPROVAL
  APPROVED
  REJECTED
}

enum MediaType {
  IMAGE
  VIDEO
}

enum BookingStatus {
  PENDING
  CONFIRMED
  CANCELLED
  COMPLETED
}

enum ReviewStatus {
  PENDING_APPROVAL
  APPROVED
  REJECTED
}

enum Gender {
  MALE
  FEMALE
  TRANS
  NON_BINARY
  OTHER
}

// Main User Model
model User {
  id        String     @id @default(cuid())
  username  String     @unique
  email     String     @unique
  password  String
  role      UserRole
  status    UserStatus @default(PENDING_VERIFICATION)

  emailVerified    <PERSON>olean   @default(false)
  emailVerifiedAt  DateTime?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  profile   Profile?
  bookings  Booking[]
  reviews   Review[]
  favorites Favorite[]

  // Admin relations
  moderatedProfiles Profile[] @relation("ModeratedBy")
  moderatedMedia    Media[]   @relation("ModeratedBy")
  moderatedReviews  Review[]  @relation("ModeratedBy")

  @@map("users")
}

// Profile Model (for Escort, Agency, Studio)
model Profile {
  id          String        @id @default(cuid())
  userId      String        @unique
  profileType UserRole      // ESCORT, AGENCY, STUDIO
  status      ProfileStatus @default(PENDING_APPROVAL)

  // Basic Info
  name        String
  alias       String?       // For escorts
  location    String
  description String?

  // Contact Info
  phone       String?
  website     String?

  // Escort specific fields
  age         Int?
  gender      Gender?
  height      Int?          // in cm
  weight      Int?          // in kg
  languages   String[]      // Array of language codes

  // Agency/Studio specific
  companyName String?

  // Media
  profileImage String?
  coverImage   String?

  // Pricing (for escorts)
  hourlyRate   Decimal?

  // Availability
  isAvailable Boolean @default(true)

  // Moderation
  moderatedBy   String?
  moderatedAt   DateTime?
  rejectionNote String?

  // Timestamps
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  user       User       @relation(fields: [userId], references: [id], onDelete: Cascade)
  moderator  User?      @relation("ModeratedBy", fields: [moderatedBy], references: [id])
  media      Media[]
  services   Service[]
  bookings   Booking[]
  reviews    Review[]
  favorites  Favorite[]

  // For agencies/studios - managed escorts
  managedEscorts EscortAgency[]

  @@map("profiles")
}

// Media Model (Images and Videos)
model Media {
  id        String      @id @default(cuid())
  profileId String
  filename  String
  originalName String
  fileUrl   String
  thumbnailUrl String?
  fileSize  Int
  mimeType  String
  type      MediaType
  status    MediaStatus @default(PENDING_APPROVAL)

  // Moderation
  moderatedBy   String?
  moderatedAt   DateTime?
  rejectionNote String?

  // Metadata
  width  Int?
  height Int?
  duration Int? // for videos in seconds

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  profile   Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)
  moderator User?   @relation("ModeratedBy", fields: [moderatedBy], references: [id])

  @@map("media")
}

// Services Model
model Service {
  id          String  @id @default(cuid())
  profileId   String
  name        String
  description String?
  price       Decimal
  duration    Int     // in minutes
  isActive    Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  profile  Profile   @relation(fields: [profileId], references: [id], onDelete: Cascade)
  bookings Booking[]

  @@map("services")
}

// Escort-Agency/Studio relationship
model EscortAgency {
  id        String @id @default(cuid())
  escortId  String
  agencyId  String
  isActive  Boolean @default(true)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  escort Profile @relation(fields: [escortId], references: [id], onDelete: Cascade)

  @@unique([escortId, agencyId])
  @@map("escort_agencies")
}

// Bookings Model
model Booking {
  id        String        @id @default(cuid())
  clientId  String
  profileId String
  serviceId String?
  status    BookingStatus @default(PENDING)

  // Booking details
  startTime DateTime
  endTime   DateTime
  totalPrice Decimal
  notes     String?

  // Contact info
  clientName  String
  clientEmail String
  clientPhone String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  client  User     @relation(fields: [clientId], references: [id])
  profile Profile  @relation(fields: [profileId], references: [id])
  service Service? @relation(fields: [serviceId], references: [id])
  review  Review?

  @@map("bookings")
}

// Reviews Model
model Review {
  id        String @id @default(cuid())
  bookingId String @unique
  clientId  String
  profileId String

  rating    Int    // 1-5 stars
  comment   String?
  status    ReviewStatus @default(PENDING_APPROVAL)

  // Moderation
  moderatedBy   String?
  moderatedAt   DateTime?
  rejectionNote String?

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  booking   Booking @relation(fields: [bookingId], references: [id], onDelete: Cascade)
  client    User    @relation(fields: [clientId], references: [id])
  profile   Profile @relation(fields: [profileId], references: [id])
  moderator User?   @relation("ModeratedBy", fields: [moderatedBy], references: [id])

  @@map("reviews")
}

// Favorites Model
model Favorite {
  id        String @id @default(cuid())
  userId    String
  profileId String

  createdAt DateTime @default(now())

  // Relations
  user    User    @relation(fields: [userId], references: [id], onDelete: Cascade)
  profile Profile @relation(fields: [profileId], references: [id], onDelete: Cascade)

  @@unique([userId, profileId])
  @@map("favorites")
}

// CMS Pages Model
model Page {
  id      String @id @default(cuid())
  slug    String @unique
  title   String
  content String
  isPublished Boolean @default(false)

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("pages")
}

// Settings Model
model Setting {
  id    String @id @default(cuid())
  key   String @unique
  value String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  @@map("settings")
}
